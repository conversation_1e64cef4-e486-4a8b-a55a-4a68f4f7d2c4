package cn.lili.controller.system;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.system.entity.dto.StoreWechatNotificationSetting;
import cn.lili.modules.system.service.StoreWechatNotificationSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 商家微信通知设置管理API
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@RestController
@Api(tags = "商家微信通知设置管理API")
@RequestMapping("/manager/system/store-wechat-notification")
public class StoreWechatNotificationSettingController {

    @Autowired
    private StoreWechatNotificationSettingService storeWechatNotificationSettingService;

    @ApiOperation(value = "获取商家微信通知设置")
    @GetMapping
    public ResultMessage<StoreWechatNotificationSetting> getStoreWechatNotificationSetting() {
        try {
            StoreWechatNotificationSetting setting = storeWechatNotificationSettingService.getStoreWechatNotificationSetting();
            return ResultUtil.success(setting);
        } catch (Exception e) {
            log.error("获取商家微信通知设置失败", e);
            return ResultUtil.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "保存商家微信通知设置")
    @PostMapping
    public ResultMessage<Boolean> saveStoreWechatNotificationSetting(@RequestBody StoreWechatNotificationSetting setting) {
        try {
            boolean result = storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);
            if (result) {
                return ResultUtil.success(true, "保存成功");
            } else {
                return ResultUtil.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存商家微信通知设置失败", e);
            return ResultUtil.error("保存失败：" + e.getMessage());
        }
    }
}
