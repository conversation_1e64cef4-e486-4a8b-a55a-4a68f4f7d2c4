package cn.lili.controller.setting;

import cn.hutool.json.JSONUtil;
import cn.lili.modules.system.entity.dto.StoreWechatNotificationSetting;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 商家微信通知设置系统接口集成测试
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class StoreWechatNotificationSettingIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testGetStoreWechatNotificationSetting() throws Exception {
        // 测试获取商家微信通知设置（应该返回默认设置）
        mockMvc.perform(get("/manager/setting/setting/get/STORE_WECHAT_NOTIFICATION_SETTING"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.result.enableStoreNotification").value(true))
                .andExpect(jsonPath("$.result.enableOrderPaidNotification").value(true))
                .andExpect(jsonPath("$.result.enableOrderDeliveredNotification").value(false))
                .andExpect(jsonPath("$.result.notificationTimeLimit").value(0));
    }

    @Test
    public void testSaveStoreWechatNotificationSetting() throws Exception {
        // 创建测试设置
        StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
        setting.setEnableStoreNotification(true);
        setting.setEnableOrderPaidNotification(true);
        setting.setEnableOrderDeliveredNotification(true);
        setting.setEnableOrderCompletedNotification(false);
        setting.setEnableOrderCancelledNotification(false);
        setting.setNotificationTimeLimit(1);
        setting.setNotificationStartHour(9);
        setting.setNotificationEndHour(22);

        String settingJson = JSONUtil.toJsonStr(setting);

        // 测试保存设置
        mockMvc.perform(put("/manager/setting/setting/put/STORE_WECHAT_NOTIFICATION_SETTING")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(settingJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证保存的设置
        mockMvc.perform(get("/manager/setting/setting/get/STORE_WECHAT_NOTIFICATION_SETTING"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.result.enableStoreNotification").value(true))
                .andExpect(jsonPath("$.result.enableOrderPaidNotification").value(true))
                .andExpect(jsonPath("$.result.enableOrderDeliveredNotification").value(true))
                .andExpect(jsonPath("$.result.enableOrderCompletedNotification").value(false))
                .andExpect(jsonPath("$.result.enableOrderCancelledNotification").value(false))
                .andExpect(jsonPath("$.result.notificationTimeLimit").value(1))
                .andExpect(jsonPath("$.result.notificationStartHour").value(9))
                .andExpect(jsonPath("$.result.notificationEndHour").value(22));
    }

    @Test
    public void testSaveAndGetStoreWechatNotificationSettingWithDisabledNotification() throws Exception {
        // 创建禁用通知的测试设置
        StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
        setting.setEnableStoreNotification(false);
        setting.setEnableOrderPaidNotification(false);
        setting.setEnableOrderDeliveredNotification(false);
        setting.setEnableOrderCompletedNotification(false);
        setting.setEnableOrderCancelledNotification(false);
        setting.setNotificationTimeLimit(0);

        String settingJson = JSONUtil.toJsonStr(setting);

        // 保存设置
        mockMvc.perform(put("/manager/setting/setting/put/STORE_WECHAT_NOTIFICATION_SETTING")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(settingJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证保存的设置
        mockMvc.perform(get("/manager/setting/setting/get/STORE_WECHAT_NOTIFICATION_SETTING"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.result.enableStoreNotification").value(false))
                .andExpect(jsonPath("$.result.enableOrderPaidNotification").value(false))
                .andExpect(jsonPath("$.result.enableOrderDeliveredNotification").value(false))
                .andExpect(jsonPath("$.result.enableOrderCompletedNotification").value(false))
                .andExpect(jsonPath("$.result.enableOrderCancelledNotification").value(false))
                .andExpect(jsonPath("$.result.notificationTimeLimit").value(0));
    }
}
