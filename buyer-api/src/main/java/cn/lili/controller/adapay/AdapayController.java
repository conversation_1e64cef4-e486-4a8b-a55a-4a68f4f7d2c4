package cn.lili.controller.adapay;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.payment.kit.CashierSupport;
import cn.lili.modules.payment.kit.dto.PayParam;
import cn.lili.modules.adapay.entity.dos.AdapayArea;
import cn.lili.modules.adapay.entity.dos.AdapayBanks;
import cn.lili.modules.adapay.service.AdapayACHService;
import cn.lili.modules.adapay.service.AdapayAreaService;
import cn.lili.modules.adapay.service.AdapayBanksService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

/**
 * 买家端,自动分账接口
 *
 * <AUTHOR>
 * @since 2020-12-18 16:59
 */
@Slf4j
@RestController
@Api(tags = "买家端,自动分账接口")
@RequestMapping("/buyer/adapay")
public class AdapayController {

    @Autowired
    private CashierSupport cashierSupport;
    @Autowired
    private AdapayACHService adapayACHService;
    @Autowired
    private AdapayAreaService adapayAreaService;
    @Autowired
    private AdapayBanksService adapayBanksService;

    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "client", value = "客户端类型", paramType = "path", allowableValues = "PC,H5,WECHAT_MP,APP")
    // })
    // @GetMapping(value = "/tradeDetail")
    // @ApiOperation(value = "获取支付详情")
    // public ResultMessage paymentParams(@Validated PayParam payParam) {
    //     CashierParam cashierParam = cashierSupport.cashierParam(payParam);
    //     return ResultUtil.data(cashierParam);
    // }

    // @ApiImplicitParams({
    //         @ApiImplicitParam(name = "paymentMethod", value = "支付方式", paramType = "path", allowableValues = "WECHAT,ALIPAY"),
    //         @ApiImplicitParam(name = "paymentClient", value = "调起方式", paramType = "path", allowableValues = "APP,NATIVE,JSAPI,H5,MP")
    // })
    // @GetMapping(value = "/pay/{paymentMethod}/{paymentClient}")
    // @ApiOperation(value = "支付")
    // public ResultMessage payment(
    //         HttpServletRequest request,
    //         HttpServletResponse response,
    //         @PathVariable String paymentMethod,
    //         @PathVariable String paymentClient,
    //         @Validated PayParam payParam) {
    //     PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.valueOf(paymentMethod);
    //     PaymentClientEnum paymentClientEnum = PaymentClientEnum.valueOf(paymentClient);

    //     try {
    //         return cashierSupport.payment(paymentMethodEnum, paymentClientEnum, request, response, payParam);
    //     } catch (ServiceException se) {
    //         log.info("支付异常", se);
    //         throw se;
    //     } catch (Exception e) {
    //         log.error("收银台支付错误", e);
    //     }
    //     return null;
    // }

    // @ApiOperation(value = "支付回调")
    // @RequestMapping(value = "/callback/{paymentMethod}", method = {RequestMethod.GET, RequestMethod.POST})
    // public ResultMessage<Object> callback(HttpServletRequest request, @PathVariable String paymentMethod) {

    //     PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.valueOf(paymentMethod);

    //     cashierSupport.callback(paymentMethodEnum, request);

    //     return ResultUtil.success(ResultCode.PAY_SUCCESS);
    // }

    @ApiOperation(value = "分账系统异步通知回调")
    @RequestMapping(value = "/callback", method = {RequestMethod.GET, RequestMethod.POST})
    public void notify(HttpServletRequest request) {

        adapayACHService.callback(request);

    }

    @ApiOperation(value = "查询支付结果")
    @GetMapping(value = "/result")
    public ResultMessage<Boolean> paymentResult(PayParam payParam) {
        return ResultUtil.data(cashierSupport.paymentResult(payParam));
    }

    //分账系统需要，获取企业所在省份、城市编码
    @GetMapping(value = "/areaCode/{id}")
    @ApiImplicitParam(name = "id", value = "地区ID", required = true, dataType = "String", paramType = "path")
    @ApiOperation(value = "通过id获取子地区")
    public ResultMessage<List<AdapayArea>> getAreaCode(@PathVariable String id) {
        return ResultUtil.data(adapayAreaService.getItem(id));
    }

    //分账系统需要，获取银行编码
    @GetMapping(value = "/bankCode")
    @ApiOperation(value = "获取银行代码")
    public ResultMessage<List<AdapayBanks>> getBankCode() {
        return ResultUtil.data(adapayBanksService.getItem());
    }

    @GetMapping(value = "/areaName/{id}")
    @ApiImplicitParam(name = "id", value = "cityID", required = true, dataType = "String", paramType = "path")
    @ApiOperation(value = "通过city id获取城市名称")
    public ResultMessage<String> getAreaNameByCode(@PathVariable String id) {
        return ResultUtil.data(adapayAreaService.getCityNameByCode(id));
    }

    @GetMapping(value = "/provName/{id}")
    @ApiImplicitParam(name = "id", value = "provID", required = true, dataType = "String", paramType = "path")
    @ApiOperation(value = "通过prov id获取省份名称")
    public ResultMessage<String> getProvNameByCode(@PathVariable String id) {
        return ResultUtil.data(adapayAreaService.getProvNameByCode(id));
    }
}
