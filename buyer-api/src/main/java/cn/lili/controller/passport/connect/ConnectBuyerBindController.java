package cn.lili.controller.passport.connect;

import cn.lili.common.security.token.Token;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.entity.dto.ConnectAuthUser;
import cn.lili.modules.connect.service.ConnectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 买家端,app/小程序 联合登录
 *
 * <AUTHOR>
 * @since 2020-11-25 19:29
 */
@RestController
@Api(tags = "买家端,联合登录接口")
@RequestMapping("/buyer/passport/connect/unionLogin")
public class ConnectBuyerBindController {

    @Autowired
    private ConnectService connectService;

    @ApiOperation(value = "unionID 绑定")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionID", value = "unionID", required = true, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "type", required = true, paramType = "query")
    })
    @PostMapping("/bind")
    public void unionIDBind(@RequestParam String unionID, @RequestParam String type) {
        connectService.bind(unionID, type);
    }
    
    @ApiOperation(value = "第三方小程序应用-unionID登录")
    @PostMapping("/unionLogin")
    public ResultMessage<Token> unionLogin(@RequestBody ConnectAuthUser authUser, @RequestHeader("uuid") String uuid) {
        try {
            return ResultUtil.data(connectService.unionLoginCallback(authUser, uuid));
        } catch (Exception e) {
            return ResultUtil.error(ResultCode.WECHAT_CONNECT_NOT_SETTING);
        }
    }    

    @ApiOperation(value = "unionID 解绑")
    @ApiImplicitParam(name = "type", value = "type", required = true, paramType = "query")
    @PostMapping("/unbind")
    public void unionIDBind(@RequestParam String type) {
        connectService.unbind(type);
    }


    @GetMapping("/list")
    @ApiOperation(value = "绑定列表")
    public ResultMessage<List<String>> bindList() {
        return ResultUtil.data(connectService.bindList());
    }

}
