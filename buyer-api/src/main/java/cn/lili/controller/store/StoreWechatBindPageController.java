package cn.lili.controller.store;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.entity.dto.ConnectAuthUser;
import cn.lili.modules.connect.service.ConnectService;
import cn.lili.modules.store.service.StoreWechatBindService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 商家微信绑定页面Controller - 用于微信扫码后的处理
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@RestController
@Api(tags = "商家微信绑定页面API")
@RequestMapping("/buyer/store/wechat-bind")
public class StoreWechatBindPageController {

    @Autowired
    private StoreWechatBindService storeWechatBindService;

    @Autowired
    private ConnectService connectService;

    @ApiOperation(value = "获取绑定信息")
    @GetMapping("/info")
    public ResultMessage<String> getBindInfo(@RequestParam String token, @RequestParam String type) {
        try {
            String status = storeWechatBindService.checkBindStatus(token, type);
            return ResultUtil.data(status);
        } catch (Exception e) {
            log.error("获取绑定信息失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "确认绑定")
    @PostMapping("/confirm")
    public ResultMessage<Boolean> confirmBind(@RequestParam String token, @RequestParam String type) {
        try {
            // 获取当前登录用户信息
            if (UserContext.getCurrentUser() == null) {
                return ResultUtil.error(ResultCode.USER_NOT_LOGIN);
            }

            // 构建ConnectAuthUser对象
            ConnectAuthUser authUser = new ConnectAuthUser();
            authUser.setUuid(UserContext.getCurrentUser().getId()); // 使用用户ID作为unionId
            authUser.setUsername(UserContext.getCurrentUser().getUsername());
            authUser.setNickname(UserContext.getCurrentUser().getNickName());

            // 处理绑定
            boolean result = storeWechatBindService.processWechatBind(authUser, token, type);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
            }
        } catch (Exception e) {
            log.error("确认绑定失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
        }
    }

    @ApiOperation(value = "取消绑定")
    @PostMapping("/cancel")
    public ResultMessage<Boolean> cancelBind(@RequestParam String token, @RequestParam String type) {
        try {
            // 这里可以实现取消绑定的逻辑，比如更新状态为CANCELLED
            return ResultUtil.data(true);
        } catch (Exception e) {
            log.error("取消绑定失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }
}
