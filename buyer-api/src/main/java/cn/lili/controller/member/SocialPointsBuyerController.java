package cn.lili.controller.member;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.member.entity.vo.MemberPointsHistoryVO;
import cn.lili.modules.member.entity.vo.SocialPointsHistoryVO;
import cn.lili.modules.member.service.SocialPointsService;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 买家端,社交生态积分接口
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@RestController
@Api(tags = "买家端,社交生态积分接口")
@RequestMapping("/buyer/member/socialPoints")
public class SocialPointsBuyerController {
    @Autowired
    private SocialPointsService socialPointsService;

    @ApiOperation(value = "获取当前登录用户社交生态积分VO")
    @GetMapping(value = "/getCurrentMemberPoints")
    public ResultMessage<MemberPointsHistoryVO> getCurrentMemberPoints() {
        return ResultUtil.data(socialPointsService.getCurrentUserPoints());
    }

    //更新社交生态积分由积分切面AOP自动处理，无需对外暴露API
    // @ApiOperation(value = "更新会员社交生态积分VO")
    // @ApiImplicitParam(name = "wxUnionId", value = "会员微信unionId", dataType = "String", paramType = "path")
    // @PostMapping(value = "/updatePointsVOByWxUnionId/{wxUnionId}")
    // public ResultMessage<MemberPointsHistoryVO> updatePointsVOByWxUnionId(@Valid SocialPointsVO socialPoints) {
    //     return ResultUtil.data(socialPointsService.updatePointsByWxUnionId(socialPoints));
    // }

    @ApiOperation(value = "获取会员社交生态积分VO")
    @ApiImplicitParam(name = "wxUnionId", value = "会员微信unionid", dataType = "String", paramType = "path")
    @GetMapping(value = "/getMemberPointsByWx/{wxUnionId}")
    public ResultMessage<MemberPointsHistoryVO> getMemberPointsHistoryVOByWxUnionId(@PathVariable String wxUnionId) {
        return ResultUtil.data(socialPointsService.getMemberPointsHistoryVOByWxUnionId(wxUnionId));
    }

    @ApiOperation(value = "分页获取社交生态积分明细")
    @ApiImplicitParam(name = "wxUnionId", value = "会员微信unionid", dataType = "String", paramType = "path")
    @GetMapping(value = "/getMemberPointsHistoryByWx/{wxUnionId}")
    public ResultMessage<IPage<SocialPointsHistoryVO>> getByPageByWxUnionId(PageVO page, @PathVariable String wxUnionId) {
        return ResultUtil.data(socialPointsService.getMemberPointsHistoryByWx(page, wxUnionId));
    }
 
    @ApiOperation(value = "分页获取当前登录用户社交生态积分明细")
    @GetMapping(value = "/getCurrentUserPointsHistory")
    public ResultMessage<IPage<SocialPointsHistoryVO>> getCurrentUserPointsHistory(PageVO page) {
        return ResultUtil.data(socialPointsService.getCurrentUserPointsHistory(page));
    }
}
