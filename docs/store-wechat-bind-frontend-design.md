# 商家端微信绑定前端设计方案

## 🎯 设计目标

为商家端Vue前端提供完整的微信扫码绑定功能，支持微信公众号和小程序两种绑定方式。

## 📋 功能流程

### 1. 绑定流程设计

```mermaid
sequenceDiagram
    participant 商家 as 商家端前端
    participant API as 商家端API
    participant 微信用户 as 微信用户
    participant 绑定页面 as 绑定确认页面

    商家->>API: 请求生成绑定二维码
    API->>商家: 返回二维码URL和绑定令牌
    商家->>商家: 显示二维码
    
    loop 轮询检查状态
        商家->>API: 检查绑定状态
        API->>商家: 返回当前状态
    end
    
    微信用户->>绑定页面: 扫码访问绑定页面
    绑定页面->>绑定页面: 显示绑定确认界面
    微信用户->>绑定页面: 确认绑定
    绑定页面->>API: 提交绑定请求
    API->>商家: 绑定成功（通过轮询获取）
```

### 2. 前端组件结构

```
src/views/store/wechat-bind/
├── index.vue                 # 主绑定页面
├── components/
│   ├── QRCodeDisplay.vue     # 二维码显示组件
│   ├── BindStatus.vue        # 绑定状态组件
│   └── BindResult.vue        # 绑定结果组件
└── bind-confirm.vue          # 绑定确认页面（微信扫码后访问）
```

## 🔧 前端实现

### 1. 主绑定页面 (index.vue)

```vue
<template>
  <div class="store-wechat-bind">
    <a-card title="微信通知绑定">
      <a-tabs v-model:activeKey="activeTab">
        <!-- 微信公众号绑定 -->
        <a-tab-pane key="official-account" tab="微信公众号">
          <WechatBindPanel 
            :bind-type="'WECHAT_OFFIACCOUNT_OPEN_ID'"
            :title="'微信公众号绑定'"
            :description="'绑定微信公众号后，您将收到订单付款等重要通知'"
          />
        </a-tab-pane>
        
        <!-- 微信小程序绑定 -->
        <a-tab-pane key="mini-program" tab="微信小程序">
          <WechatBindPanel 
            :bind-type="'WECHAT_MP_OPEN_ID'"
            :title="'微信小程序绑定'"
            :description="'绑定微信小程序后，您将收到订单付款等重要通知'"
          />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script>
import WechatBindPanel from './components/WechatBindPanel.vue'

export default {
  name: 'StoreWechatBind',
  components: {
    WechatBindPanel
  },
  data() {
    return {
      activeTab: 'official-account'
    }
  }
}
</script>
```

### 2. 绑定面板组件 (WechatBindPanel.vue)

```vue
<template>
  <div class="wechat-bind-panel">
    <div class="bind-info">
      <h3>{{ title }}</h3>
      <p>{{ description }}</p>
    </div>

    <!-- 未绑定状态 -->
    <div v-if="!isBound" class="bind-section">
      <a-button 
        type="primary" 
        @click="startBind"
        :loading="loading"
      >
        开始绑定
      </a-button>
    </div>

    <!-- 绑定中状态 -->
    <div v-if="showQRCode" class="qrcode-section">
      <div class="qrcode-container">
        <img :src="qrCodeUrl" alt="绑定二维码" />
        <div class="qrcode-tips">
          <p>请使用微信扫描二维码</p>
          <p>扫码后在手机上确认绑定</p>
        </div>
      </div>
      
      <div class="bind-status">
        <a-spin :spinning="polling">
          <p>{{ statusText }}</p>
        </a-spin>
      </div>
      
      <a-button @click="cancelBind">取消绑定</a-button>
    </div>

    <!-- 已绑定状态 -->
    <div v-if="isBound" class="bound-section">
      <a-result
        status="success"
        title="已绑定微信"
        :sub-title="`绑定时间：${bindTime}`"
      >
        <template #extra>
          <a-space>
            <a-switch 
              v-model:checked="notificationEnabled"
              @change="toggleNotification"
            />
            <span>接收通知</span>
            <a-button danger @click="unbind">解除绑定</a-button>
          </a-space>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script>
import { storeWechatBindAPI } from '@/api/store'

export default {
  name: 'WechatBindPanel',
  props: {
    bindType: {
      type: String,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      showQRCode: false,
      polling: false,
      qrCodeUrl: '',
      bindToken: '',
      isBound: false,
      notificationEnabled: true,
      bindTime: '',
      statusText: '等待扫码...',
      pollTimer: null
    }
  },
  mounted() {
    this.checkBindStatus()
  },
  beforeUnmount() {
    this.stopPolling()
  },
  methods: {
    // 检查绑定状态
    async checkBindStatus() {
      try {
        const response = await storeWechatBindAPI.getBindStatus()
        const bindInfo = response.result.find(item => item.unionType === this.bindType)
        if (bindInfo) {
          this.isBound = true
          this.notificationEnabled = bindInfo.enableNotification
          this.bindTime = bindInfo.createTime
        }
      } catch (error) {
        console.error('检查绑定状态失败:', error)
      }
    },

    // 开始绑定
    async startBind() {
      this.loading = true
      try {
        let response
        if (this.bindType === 'WECHAT_OFFIACCOUNT_OPEN_ID') {
          response = await storeWechatBindAPI.generateOfficialAccountQRCode()
        } else {
          response = await storeWechatBindAPI.generateMiniProgramQRCode()
        }
        
        this.qrCodeUrl = response.result.qrCodeUrl
        this.bindToken = response.result.bindToken
        this.showQRCode = true
        this.startPolling()
      } catch (error) {
        this.$message.error('生成绑定二维码失败')
        console.error('生成绑定二维码失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 开始轮询
    startPolling() {
      this.polling = true
      this.pollTimer = setInterval(async () => {
        try {
          const response = await storeWechatBindAPI.checkBindStatus(this.bindToken, this.bindType)
          const status = response.result
          
          switch (status) {
            case 'WAITING':
              this.statusText = '等待扫码...'
              break
            case 'SCANNED':
              this.statusText = '已扫码，请在手机上确认绑定'
              break
            case 'SUCCESS':
              this.statusText = '绑定成功！'
              this.onBindSuccess()
              break
            case 'EXPIRED':
              this.statusText = '二维码已过期'
              this.stopPolling()
              break
            case 'FAILED':
              this.statusText = '绑定失败'
              this.stopPolling()
              break
          }
        } catch (error) {
          console.error('检查绑定状态失败:', error)
        }
      }, 2000)
    },

    // 停止轮询
    stopPolling() {
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
        this.pollTimer = null
      }
      this.polling = false
    },

    // 绑定成功
    onBindSuccess() {
      this.stopPolling()
      this.showQRCode = false
      this.isBound = true
      this.bindTime = new Date().toLocaleString()
      this.$message.success('微信绑定成功！')
    },

    // 取消绑定
    cancelBind() {
      this.stopPolling()
      this.showQRCode = false
    },

    // 切换通知状态
    async toggleNotification(enabled) {
      try {
        await storeWechatBindAPI.updateNotificationStatus(this.bindType, enabled)
        this.$message.success(enabled ? '已开启通知' : '已关闭通知')
      } catch (error) {
        this.$message.error('更新通知状态失败')
        this.notificationEnabled = !enabled // 回滚状态
      }
    },

    // 解除绑定
    async unbind() {
      try {
        await storeWechatBindAPI.unbind(this.bindType)
        this.isBound = false
        this.$message.success('解除绑定成功')
      } catch (error) {
        this.$message.error('解除绑定失败')
      }
    }
  }
}
</script>

<style scoped>
.wechat-bind-panel {
  padding: 20px;
}

.qrcode-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 20px 0;
}

.qrcode-container img {
  width: 200px;
  height: 200px;
  border: 1px solid #d9d9d9;
}

.qrcode-tips {
  margin-top: 10px;
  text-align: center;
  color: #666;
}

.bind-status {
  text-align: center;
  margin: 20px 0;
}
</style>
```

### 3. API 封装

```javascript
// src/api/store/wechat-bind.js
import request from '@/utils/request'

export const storeWechatBindAPI = {
  // 生成微信公众号绑定二维码
  generateOfficialAccountQRCode() {
    return request({
      url: '/store/seller/store/wechat-bind/qrcode/official-account',
      method: 'GET'
    })
  },

  // 生成微信小程序绑定二维码
  generateMiniProgramQRCode() {
    return request({
      url: '/store/seller/store/wechat-bind/qrcode/mini-program',
      method: 'GET'
    })
  },

  // 检查绑定状态
  checkBindStatus(bindToken, unionType) {
    return request({
      url: '/store/seller/store/wechat-bind/status',
      method: 'GET',
      params: { bindToken, unionType }
    })
  },

  // 获取绑定状态
  getBindStatus() {
    return request({
      url: '/store/seller/store/wechat-bind/bind-status',
      method: 'GET'
    })
  },

  // 更新通知状态
  updateNotificationStatus(unionType, enable) {
    return request({
      url: '/store/seller/store/connect/notification',
      method: 'PUT',
      params: { unionType, enable }
    })
  },

  // 解除绑定
  unbind(unionType) {
    return request({
      url: '/store/seller/store/wechat-bind/unbind',
      method: 'DELETE',
      params: { unionType }
    })
  }
}
```

### 4. 绑定确认页面 (bind-confirm.vue)

```vue
<template>
  <div class="bind-confirm-page">
    <div class="confirm-container">
      <div class="header">
        <h2>商家微信绑定确认</h2>
      </div>
      
      <div v-if="loading" class="loading">
        <a-spin size="large" />
        <p>正在加载...</p>
      </div>
      
      <div v-else-if="bindInfo" class="bind-info">
        <a-result
          status="info"
          title="确认绑定微信通知"
          :sub-title="`绑定类型：${bindTypeText}`"
        >
          <template #extra>
            <a-space>
              <a-button type="primary" @click="confirmBind" :loading="confirming">
                确认绑定
              </a-button>
              <a-button @click="cancelBind">
                取消
              </a-button>
            </a-space>
          </template>
        </a-result>
      </div>
      
      <div v-else class="error">
        <a-result
          status="error"
          title="绑定链接已失效"
          sub-title="请重新扫码绑定"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { storeWechatBindPageAPI } from '@/api/store'

export default {
  name: 'StoreWechatBindConfirm',
  data() {
    return {
      loading: true,
      confirming: false,
      bindInfo: null,
      token: '',
      type: ''
    }
  },
  computed: {
    bindTypeText() {
      return this.type === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序'
    }
  },
  mounted() {
    this.token = this.$route.query.token
    this.type = this.$route.query.type
    this.loadBindInfo()
  },
  methods: {
    async loadBindInfo() {
      try {
        const response = await storeWechatBindPageAPI.getBindInfo(this.token, this.type)
        if (response.result === 'WAITING' || response.result === 'SCANNED') {
          this.bindInfo = { status: response.result }
        } else {
          this.bindInfo = null
        }
      } catch (error) {
        console.error('加载绑定信息失败:', error)
        this.bindInfo = null
      } finally {
        this.loading = false
      }
    },

    async confirmBind() {
      this.confirming = true
      try {
        await storeWechatBindPageAPI.confirmBind(this.token, this.type)
        this.$message.success('绑定成功！')
        // 可以跳转到成功页面或关闭页面
        window.close()
      } catch (error) {
        this.$message.error('绑定失败，请重试')
      } finally {
        this.confirming = false
      }
    },

    cancelBind() {
      window.close()
    }
  }
}
</script>

<style scoped>
.bind-confirm-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.confirm-container {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.loading {
  text-align: center;
  padding: 40px;
}
</style>
```

## 🚀 部署说明

1. **路由配置**：在商家端Vue项目中添加路由配置
2. **菜单配置**：在商家端后台菜单中添加"微信通知绑定"菜单项
3. **权限配置**：确保商家有访问绑定功能的权限
4. **域名配置**：确保`lili.domain.seller`配置正确，用于生成二维码链接

这个设计方案提供了完整的商家端微信绑定功能，包括二维码生成、状态轮询、绑定确认等完整流程。
