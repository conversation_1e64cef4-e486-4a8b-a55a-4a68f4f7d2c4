# 微信获取用户信息API实现

## 🎯 API接口

我已经在seller-api中实现了 `/store/wechat-bind/getUserInfo` 接口。

### 接口详情

```java
@ApiOperation(value = "通过微信授权码获取用户信息")
@GetMapping("/getUserInfo")
public ResultMessage<Map<String, Object>> getUserInfo(@RequestParam String code)
```

**请求参数**：
- `code`: 微信授权码（必需）

**返回数据**：
```json
{
  "success": true,
  "result": {
    "openid": "微信openid",
    "unionid": "微信unionid", 
    "nickname": "微信昵称",
    "headimgurl": "微信头像URL",
    "sex": 1,
    "city": "城市",
    "province": "省份",
    "country": "国家"
  }
}
```

## 🔧 前端调用示例

### 1. API封装

```javascript
// src/api/store/wechat-bind.js
export const storeWechatBindAPI = {
  // 通过微信授权码获取用户信息
  getWechatUserInfoByCode(code) {
    return request({
      url: '/store/wechat-bind/getUserInfo',
      method: 'GET',
      params: { code }
    })
  }
}
```

### 2. 前端调用

根据您提供的前端代码，可以这样调用：

```javascript
// 通过code获取用户信息
async getUserInfoByCode(code) {
  try {
    // 验证state参数
    const params = getUrlParams()
    const state = params.state
    const savedStateData = sessionStorage.getItem('wechat_auth_state_data')

    if (savedStateData) {
      const stateData = JSON.parse(savedStateData)

      // 验证state
      if (state && state !== stateData.state) {
        throw new Error('授权验证失败，请重新授权')
      }

      // 确保参数已恢复
      if (!this.token || !this.unionType) {
        this.token = stateData.token
        this.unionType = stateData.type
      }
    } else if (state) {
      throw new Error('授权状态丢失，请重新授权')
    }

    // 最终验证参数
    if (!this.token || !this.unionType) {
      throw new Error('绑定参数丢失，请重新扫描二维码')
    }

    // 调用后端API获取用户信息
    const response = await storeWechatBindAPI.getWechatUserInfoByCode(code)

    if (response.success) {
      const userInfo = response.result

      // 清理URL中的授权参数
      cleanUrlParams(['code', 'state'])

      // 清理sessionStorage
      sessionStorage.removeItem('wechat_auth_state_data')

      return {
        unionId: userInfo.unionid || '',
        openId: userInfo.openid || '',
        nickname: userInfo.nickname || '微信用户',
        avatar: userInfo.headimgurl || '',
        gender: userInfo.sex || 0,
        city: userInfo.city || '',
        province: userInfo.province || '',
        country: userInfo.country || '',
        code: code
      }
    } else {
      throw new Error(response.message || '获取用户信息失败')
    }
  } catch (error) {
    console.error('通过code获取用户信息失败:', error)
    throw error
  }
}
```

## ⚠️ 重要配置

### 1. 微信应用配置

接口中的 `getWechatSetting()` 方法目前返回null，需要配置微信应用信息：

```java
private WechatConnectSettingItem getWechatSetting() {
    WechatConnectSettingItem wechatSetting = new WechatConnectSettingItem();
    wechatSetting.setAppId("your_wechat_appid");      // 微信AppID
    wechatSetting.setAppSecret("your_wechat_secret"); // 微信AppSecret
    return wechatSetting;
}
```

### 2. 环境变量配置

建议通过环境变量配置：

```yaml
# application.yml
wechat:
  appId: ${WECHAT_APPID:your_default_appid}
  appSecret: ${WECHAT_SECRET:your_default_secret}
```

### 3. 权限白名单

已添加到白名单，无需登录即可访问：

```yaml
ignored:
  urls:
    - /store/wechat-bind/getUserInfo
```

## 🔄 完整流程

### 1. 微信授权流程

```javascript
// 1. 跳转到微信授权页面
redirectToWechatAuth() {
  const appId = process.env.VUE_APP_WECHAT_APPID
  const redirectUri = encodeURIComponent(window.location.href)
  const scope = 'snsapi_userinfo'
  const state = generateRandomString()
  
  const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
  
  window.location.href = authUrl
}

// 2. 授权回调后获取用户信息
async getWechatUserInfo() {
  const params = getUrlParams()
  const code = params.code

  if (code) {
    return await this.getUserInfoByCode(code)
  } else {
    this.redirectToWechatAuth()
    throw new Error('正在跳转到微信授权页面...')
  }
}
```

### 2. 绑定确认流程

```javascript
// 3. 确认绑定
async confirmBind() {
  try {
    const userInfo = await this.getWechatUserInfo()
    
    const response = await storeWechatBindAPI.confirmBind(
      this.token,
      this.unionType, 
      userInfo.unionId,
      userInfo.nickname,
      userInfo.avatar
    )
    
    if (response.success) {
      this.$message.success('绑定成功！')
      setTimeout(() => window.close(), 2000)
    }
  } catch (error) {
    this.$message.error('绑定失败：' + error.message)
  }
}
```

## 🚀 部署说明

### 1. 后端部署

1. **配置微信应用信息**
2. **重启seller-api应用**
3. **验证接口可访问性**

### 2. 前端配置

确保前端环境变量配置正确：

```bash
# .env.production
VUE_APP_WECHAT_APPID=your_wechat_appid
```

### 3. 测试验证

```bash
# 测试getUserInfo接口
curl -X GET "https://store-api.dboss.pro/store/wechat-bind/getUserInfo?code=test_code"
```

## 📋 注意事项

1. **微信配置**：需要在微信公众平台配置正确的授权回调域名
2. **HTTPS要求**：微信要求使用HTTPS协议
3. **code有效期**：微信授权码只能使用一次，且有效期很短
4. **unionid获取**：需要用户关注公众号或授权小程序才能获取unionid

这样就完成了微信获取用户信息API的实现，前端可以按照您提供的代码逻辑正常调用！
