# API路径修正说明

## 🎯 问题描述

在store-api中发现仍存在 `/store/seller/store` 开头的API路径，需要将其修改为 `/store` 开头。

## ✅ 修正内容

### 1. Controller位置调整

**修正前**：
- 位置：`seller-api/src/main/java/cn/lili/controller/store/StoreWechatBindController.java`
- 路径：`@RequestMapping("/store/seller/store/wechat-bind")`

**修正后**：
- 位置：`store-api/src/main/java/cn/lili/controller/store/StoreWechatBindController.java`
- 路径：`@RequestMapping("/store/wechat-bind")`

### 2. API接口路径

修正后的API接口路径：

```java
// 商家微信绑定API (store-api)
GET    /store/wechat-bind/qrcode/official-account    // 生成微信公众号绑定二维码
GET    /store/wechat-bind/qrcode/mini-program        // 生成微信小程序绑定二维码
GET    /store/wechat-bind/status                     // 检查绑定状态
POST   /store/wechat-bind/confirm                    // 确认绑定
POST   /store/wechat-bind/direct-bind               // 直接绑定微信
GET    /store/wechat-bind/bind-status               // 获取绑定状态
DELETE /store/wechat-bind/unbind                    // 解绑微信
```

### 3. 前端API调用

前端调用时使用的API路径：

```javascript
// src/api/store/wechat-bind.js
export const storeWechatBindAPI = {
  // 生成微信公众号绑定二维码
  generateOfficialAccountQRCode() {
    return request({
      url: '/store/wechat-bind/qrcode/official-account',
      method: 'GET'
    })
  },

  // 生成微信小程序绑定二维码
  generateMiniProgramQRCode() {
    return request({
      url: '/store/wechat-bind/qrcode/mini-program',
      method: 'GET'
    })
  },

  // 检查绑定状态
  checkBindStatus(bindToken, unionType) {
    return request({
      url: '/store/wechat-bind/status',
      method: 'GET',
      params: { bindToken, unionType }
    })
  },

  // 确认绑定
  confirmBind(bindToken, unionType, memberId, memberName) {
    return request({
      url: '/store/wechat-bind/confirm',
      method: 'POST',
      params: { bindToken, unionType, memberId, memberName }
    })
  },

  // 获取绑定状态
  getBindStatus() {
    return request({
      url: '/store/wechat-bind/bind-status',
      method: 'GET'
    })
  },

  // 解除绑定
  unbind(unionType) {
    return request({
      url: '/store/wechat-bind/unbind',
      method: 'DELETE',
      params: { unionType }
    })
  }
}
```

## 🔧 架构说明

### 1. API服务分工

- **store-api**：商家端业务API，路径以 `/store` 开头
- **seller-api**：商家端管理API，路径以 `/store/seller` 开头
- **buyer-api**：买家端API，路径以 `/buyer` 开头

### 2. 微信绑定功能归属

微信绑定功能属于商家端核心业务，应该放在 **store-api** 中：

```
store-api/
├── StoreWechatBindController     // 微信绑定Controller
├── StoreConnectController        // 商家连接管理Controller
└── 其他商家业务Controller
```

### 3. 完整的API路径规范

```
# store-api (商家端业务API)
/store/wechat-bind/*              // 微信绑定相关
/store/connect/*                  // 商家连接管理
/store/order/*                    // 商家订单管理
/store/goods/*                    // 商家商品管理

# seller-api (商家端管理API)  
/store/seller/store/*             // 店铺管理
/store/seller/member/*            // 商家会员管理
/store/seller/statistics/*        // 商家统计分析

# buyer-api (买家端API)
/buyer/member/*                   // 买家会员
/buyer/order/*                    // 买家订单
/buyer/goods/*                    // 商品浏览
```

## 📋 修正影响

### 1. 前端调用

前端需要确保API调用指向正确的服务：

```javascript
// 商家端前端配置
const API_BASE_URL = {
  store: 'https://store-api.dboss.pro',      // 商家业务API
  seller: 'https://seller-api.dboss.pro',   // 商家管理API
  buyer: 'https://buyer-api.dboss.pro'      // 买家API
}

// 微信绑定API调用
const storeWechatBindAPI = {
  generateQRCode() {
    return request({
      baseURL: API_BASE_URL.store,
      url: '/store/wechat-bind/qrcode/official-account',
      method: 'GET'
    })
  }
}
```

### 2. 网关路由

如果使用API网关，需要配置正确的路由：

```yaml
# API Gateway 路由配置
routes:
  - id: store-api
    uri: http://store-api:8888
    predicates:
      - Path=/store/**
    
  - id: seller-api  
    uri: http://seller-api:8889
    predicates:
      - Path=/store/seller/**
      
  - id: buyer-api
    uri: http://buyer-api:8890
    predicates:
      - Path=/buyer/**
```

### 3. 文档更新

需要更新相关文档中的API路径：

- Swagger文档
- 前端开发文档
- API接口文档
- 部署配置文档

## ✅ 验证方法

### 1. 检查Controller位置

```bash
# 确认Controller在正确的模块中
find . -name "StoreWechatBindController.java"
# 应该输出：./store-api/src/main/java/cn/lili/controller/store/StoreWechatBindController.java
```

### 2. 检查API路径

```bash
# 启动store-api后检查接口
curl -X GET "https://store-api.dboss.pro/store/wechat-bind/qrcode/official-account" \
  -H "Authorization: Bearer your-token"
```

### 3. 检查Swagger文档

访问 `https://store-api.dboss.pro/swagger-ui.html` 确认API路径正确。

## 🚀 部署注意事项

1. **重新构建**：修改后需要重新构建store-api
2. **配置检查**：确认API网关路由配置正确
3. **前端更新**：前端需要更新API调用路径
4. **测试验证**：完整测试微信绑定流程

修正后，微信绑定功能的API路径将更加规范和清晰！
