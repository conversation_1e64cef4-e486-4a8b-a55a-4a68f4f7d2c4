# Seller-API 路径规范说明

## 🎯 路径规范

在seller-api中，我们使用以下路径规范：

### 1. 商家业务相关API
使用 `/store` 前缀，表示商家端核心业务功能：

```java
@RequestMapping("/store/wechat-bind")    // 微信绑定功能
@RequestMapping("/store/connect")        // 商家连接管理
@RequestMapping("/store/order")          // 商家订单管理
@RequestMapping("/store/goods")          // 商家商品管理
```

### 2. 商家管理相关API
使用 `/store/seller` 前缀，表示商家端管理功能：

```java
@RequestMapping("/store/seller/store")      // 店铺管理
@RequestMapping("/store/seller/member")     // 商家会员管理
@RequestMapping("/store/seller/statistics") // 商家统计分析
```

## 📋 当前API接口列表

### 微信绑定相关 (`/store/wechat-bind`)

```java
// StoreWechatBindController
GET    /store/wechat-bind/qrcode/official-account    // 生成微信公众号绑定二维码
GET    /store/wechat-bind/qrcode/mini-program        // 生成微信小程序绑定二维码
GET    /store/wechat-bind/status                     // 检查绑定状态
POST   /store/wechat-bind/confirm                    // 确认绑定
POST   /store/wechat-bind/direct-bind               // 直接绑定微信
GET    /store/wechat-bind/bind-status               // 获取绑定状态
DELETE /store/wechat-bind/unbind                    // 解绑微信
```

### 商家连接管理 (`/store/connect`)

```java
// StoreConnectController
POST   /store/connect/bind                          // 绑定商家微信
DELETE /store/connect/unbind                        // 解绑商家微信
GET    /store/connect/list                          // 获取绑定列表
PUT    /store/connect/notification                  // 更新通知状态
```

## 🔧 前端API调用

### 1. 微信绑定API

```javascript
// src/api/store/wechat-bind.js
export const storeWechatBindAPI = {
  // 生成微信公众号绑定二维码
  generateOfficialAccountQRCode() {
    return request({
      url: '/store/wechat-bind/qrcode/official-account',
      method: 'GET'
    })
  },

  // 生成微信小程序绑定二维码
  generateMiniProgramQRCode() {
    return request({
      url: '/store/wechat-bind/qrcode/mini-program',
      method: 'GET'
    })
  },

  // 检查绑定状态
  checkBindStatus(bindToken, unionType) {
    return request({
      url: '/store/wechat-bind/status',
      method: 'GET',
      params: { bindToken, unionType }
    })
  },

  // 确认绑定
  confirmBind(bindToken, unionType, memberId, memberName) {
    return request({
      url: '/store/wechat-bind/confirm',
      method: 'POST',
      params: { bindToken, unionType, memberId, memberName }
    })
  },

  // 获取绑定状态
  getBindStatus() {
    return request({
      url: '/store/wechat-bind/bind-status',
      method: 'GET'
    })
  },

  // 解除绑定
  unbind(unionType) {
    return request({
      url: '/store/wechat-bind/unbind',
      method: 'DELETE',
      params: { unionType }
    })
  }
}
```

### 2. 商家连接管理API

```javascript
// src/api/store/connect.js
export const storeConnectAPI = {
  // 绑定商家微信
  bindWechat(unionType) {
    return request({
      url: '/store/connect/bind',
      method: 'POST',
      params: { unionType }
    })
  },

  // 解绑商家微信
  unbindWechat(unionType) {
    return request({
      url: '/store/connect/unbind',
      method: 'DELETE',
      params: { unionType }
    })
  },

  // 获取绑定列表
  getBindList() {
    return request({
      url: '/store/connect/list',
      method: 'GET'
    })
  },

  // 更新通知状态
  updateNotificationStatus(unionType, enable) {
    return request({
      url: '/store/connect/notification',
      method: 'PUT',
      params: { unionType, enable }
    })
  }
}
```

## 🌐 网关路由配置

如果使用API网关，建议配置如下：

```yaml
# API Gateway 路由配置
spring:
  cloud:
    gateway:
      routes:
        - id: seller-api-store
          uri: http://seller-api:8888
          predicates:
            - Path=/store/**
          filters:
            - StripPrefix=0
            
        - id: seller-api-seller
          uri: http://seller-api:8888
          predicates:
            - Path=/store/seller/**
          filters:
            - StripPrefix=0
```

## 📱 前端配置

### 1. API Base URL配置

```javascript
// config/api.js
const API_CONFIG = {
  // 商家端API
  SELLER_API: process.env.VUE_APP_SELLER_API || 'https://store-api.dboss.pro',
  
  // 其他API
  BUYER_API: process.env.VUE_APP_BUYER_API || 'https://buyer-api.dboss.pro',
  MANAGER_API: process.env.VUE_APP_MANAGER_API || 'https://manager-api.dboss.pro'
}

export default API_CONFIG
```

### 2. Request拦截器配置

```javascript
// utils/request.js
import axios from 'axios'
import API_CONFIG from '@/config/api'

const request = axios.create({
  baseURL: API_CONFIG.SELLER_API,
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(config => {
  // 添加认证token
  const token = localStorage.getItem('seller_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

export default request
```

## 🚀 部署说明

### 1. 环境变量配置

```bash
# 生产环境配置
export SELLER_API_PORT=8888
export SELLER_API_DOMAIN=https://store-api.dboss.pro

# 启动seller-api
java -jar seller-api.jar \
  --server.port=${SELLER_API_PORT} \
  --lili.domain.seller=https://seller.dboss.pro
```

### 2. Nginx配置

```nginx
# nginx.conf
upstream seller-api {
    server 127.0.0.1:8888;
}

server {
    listen 443 ssl;
    server_name store-api.dboss.pro;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location /store/ {
        proxy_pass http://seller-api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## ✅ 验证方法

### 1. 检查API路径

```bash
# 检查微信绑定API
curl -X GET "https://store-api.dboss.pro/store/wechat-bind/qrcode/mini-program" \
  -H "Authorization: Bearer your-token"

# 检查连接管理API
curl -X GET "https://store-api.dboss.pro/store/connect/list" \
  -H "Authorization: Bearer your-token"
```

### 2. 检查Swagger文档

访问 `https://store-api.dboss.pro/swagger-ui.html` 确认API路径正确。

## 📋 总结

- ✅ 使用seller-api统一管理商家端API
- ✅ `/store/*` 路径用于商家核心业务
- ✅ `/store/seller/*` 路径用于商家管理功能
- ✅ API路径简洁清晰，符合RESTful规范
- ✅ 前端调用方便，易于维护

这样的路径规范既保持了API的一致性，又便于前端开发和维护！
