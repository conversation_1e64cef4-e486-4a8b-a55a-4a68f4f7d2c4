# 商家微信绑定正确架构设计

## 🚨 问题分析

### 当前问题
您遇到的问题是：
- 二维码URL指向：`https://store-api.dboss.pro/store-wechat-bind?token=xxx&type=xxx`
- 这是一个**API域名**，但实际应该指向**前端页面**

### 架构错误
我之前的设计中存在架构混淆：
1. ❌ 二维码指向了API域名
2. ❌ 创建了buyer-api中的绑定页面Controller
3. ❌ 混淆了前端页面和API接口的职责

## ✅ 正确的架构设计

### 1. 整体架构图

```mermaid
sequenceDiagram
    participant 商家 as 商家端前端<br/>(Vue.js)
    participant SellerAPI as store-api<br/>(后端API)
    participant 微信用户 as 微信用户
    participant 绑定页面 as 商家端前端<br/>绑定确认页面
    participant BuyerAPI as buyer-api<br/>(可选)

    商家->>SellerAPI: 1. 请求生成二维码
    SellerAPI->>商家: 2. 返回二维码(指向前端页面)
    
    微信用户->>绑定页面: 3. 扫码访问前端绑定页面
    绑定页面->>SellerAPI: 4. 调用确认绑定接口
    SellerAPI->>SellerAPI: 5. 处理绑定逻辑
    
    商家->>SellerAPI: 6. 轮询检查绑定状态
    SellerAPI->>商家: 7. 返回绑定成功状态
```

### 2. 关键要点

1. **二维码URL**：必须指向商家端前端域名，不是API域名
2. **绑定页面**：是商家端前端的Vue页面，不是API接口
3. **API调用**：绑定页面通过AJAX调用store-api完成绑定

## 🔧 修复方案

### 1. 后端配置修复

**配置文件修改**：
```yaml
# application-prod.yml
lili:
  domain:
    seller: https://store.dboss.pro  # 商家端前端域名，不是API域名
```

**当前错误配置**：
```yaml
# ❌ 错误：指向API域名
lili:
  domain:
    seller: https://store-api.dboss.pro
```

**正确配置**：
```yaml
# ✅ 正确：指向前端域名
lili:
  domain:
    seller: https://store.dboss.pro
```

### 2. 域名说明

根据您的生产环境，应该是：
- **API域名**：`https://store-api.dboss.pro` (后端API)
- **前端域名**：`https://store.dboss.pro` (商家端前端)

二维码应该指向前端域名！

### 3. 前端路由配置

在商家端Vue项目中需要添加绑定确认页面路由：

```javascript
// router/index.js
{
  path: '/store-wechat-bind',
  name: 'StoreWechatBindConfirm',
  component: () => import('@/views/store/wechat-bind/confirm.vue'),
  meta: {
    title: '微信绑定确认',
    requiresAuth: false // 允许未登录访问
  }
}
```

### 4. 绑定确认页面

```vue
<!-- views/store/wechat-bind/confirm.vue -->
<template>
  <div class="bind-confirm-page">
    <div class="confirm-container">
      <div v-if="loading" class="loading">
        <a-spin size="large" />
        <p>正在加载...</p>
      </div>
      
      <div v-else-if="bindInfo" class="bind-info">
        <a-result
          status="info"
          title="确认绑定微信通知"
          :sub-title="`绑定类型：${bindTypeText}`"
        >
          <template #extra>
            <a-space>
              <a-button type="primary" @click="confirmBind" :loading="confirming">
                确认绑定
              </a-button>
              <a-button @click="cancelBind">
                取消
              </a-button>
            </a-space>
          </template>
        </a-result>
      </div>
      
      <div v-else class="error">
        <a-result
          status="error"
          title="绑定链接已失效"
          sub-title="请重新扫码绑定"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { storeWechatBindAPI } from '@/api/store'

export default {
  name: 'StoreWechatBindConfirm',
  data() {
    return {
      loading: true,
      confirming: false,
      bindInfo: null,
      token: '',
      type: ''
    }
  },
  computed: {
    bindTypeText() {
      return this.type === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序'
    }
  },
  mounted() {
    this.token = this.$route.query.token
    this.type = this.$route.query.type
    this.loadBindInfo()
  },
  methods: {
    async loadBindInfo() {
      try {
        // 调用store-api检查绑定状态
        const response = await storeWechatBindAPI.checkBindStatus(this.token, this.type)
        if (response.result === 'WAITING' || response.result === 'SCANNED') {
          this.bindInfo = { status: response.result }
        } else {
          this.bindInfo = null
        }
      } catch (error) {
        console.error('加载绑定信息失败:', error)
        this.bindInfo = null
      } finally {
        this.loading = false
      }
    },

    async confirmBind() {
      this.confirming = true
      try {
        // 这里需要调用store-api的绑定接口
        // 但需要用户身份信息，可能需要先登录或使用特殊的绑定接口
        await storeWechatBindAPI.confirmBind(this.token, this.type)
        this.$message.success('绑定成功！')
        window.close()
      } catch (error) {
        this.$message.error('绑定失败，请重试')
      } finally {
        this.confirming = false
      }
    },

    cancelBind() {
      window.close()
    }
  }
}
</script>
```

## 🚀 立即修复步骤

### 1. 修改配置文件

```yaml
# 在生产环境配置文件中修改
lili:
  domain:
    seller: https://store.dboss.pro  # 改为前端域名
```

### 2. 重启应用

```bash
# 重启store-api应用
systemctl restart store-api
# 或者
docker restart store-api-container
```

### 3. 验证修复

1. 重新生成二维码
2. 检查二维码URL是否变为：`https://store.dboss.pro/store-wechat-bind?token=xxx&type=xxx`
3. 确保前端已部署绑定确认页面

## ⚠️ 重要注意事项

### 1. 域名配置
- `lili.domain.seller` = 商家端前端域名
- `lili.api.store` = 商家端API域名
- 二者不能混淆！

### 2. 前端部署
- 确保商家端前端已部署到 `https://store.dboss.pro`
- 确保绑定确认页面路由已配置
- 确保页面可以正常访问

### 3. API调用
- 绑定确认页面需要调用store-api完成绑定
- 可能需要处理跨域问题
- 需要考虑用户身份验证问题

## 🔍 问题排查

如果修复后仍有问题，请检查：

1. **配置是否生效**：
   ```bash
   # 检查应用日志
   tail -f /path/to/store-api.log
   ```

2. **前端是否部署**：
   ```bash
   curl https://store.dboss.pro/store-wechat-bind
   ```

3. **DNS解析是否正确**：
   ```bash
   nslookup store.dboss.pro
   ```

修复后，二维码应该指向前端页面，微信扫码后可以正常访问绑定确认页面！
