# 商家端微信绑定完整设计方案

## 🎯 设计概述

为商家端提供完整的微信扫码绑定功能，解决商家微信通知的绑定问题。支持微信公众号和小程序两种绑定方式，提供友好的用户体验。

## 🏗️ 系统架构

### 1. 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   商家端前端     │    │   商家端API     │    │   绑定确认页面   │
│   (Vue.js)      │    │  (seller-api)   │    │   (buyer-api)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ 1.请求生成二维码        │                       │
         ├──────────────────────→│                       │
         │                       │                       │
         │ 2.返回二维码和令牌      │                       │
         │←──────────────────────┤                       │
         │                       │                       │
         │ 3.轮询检查状态          │                       │
         ├──────────────────────→│                       │
         │                       │                       │
         │                       │ 4.微信用户扫码访问     │
         │                       │←─────────────────────┤
         │                       │                       │
         │                       │ 5.确认绑定            │
         │                       │←─────────────────────┤
         │                       │                       │
         │ 6.绑定成功状态          │                       │
         │←──────────────────────┤                       │
```

### 2. 数据流设计

1. **商家端操作流程**：
   - 商家在后台点击"绑定微信"
   - 系统生成唯一绑定令牌和二维码
   - 商家端显示二维码并开始轮询状态

2. **微信用户操作流程**：
   - 微信用户扫描二维码
   - 跳转到绑定确认页面
   - 用户确认绑定操作
   - 系统完成绑定并更新状态

3. **状态同步机制**：
   - 使用Redis缓存存储绑定状态
   - 前端通过轮询获取最新状态
   - 支持实时状态更新

## 📊 数据库设计

### 1. 现有表结构

- `li_connect` - 用户微信绑定表（已存在）
- `li_store_connect` - 商家微信绑定表（新增）

### 2. 缓存设计

```
Redis Key: STORE_WECHAT_BIND:{bindToken}
Value: {
  "storeId": "店铺ID",
  "memberId": "会员ID", 
  "unionType": "绑定类型",
  "status": "绑定状态",
  "createTime": "创建时间"
}
TTL: 300秒（5分钟）
```

## 🔧 后端实现

### 1. 核心接口设计

#### 商家端API (seller-api)

```java
// 生成绑定二维码
GET /store/wechat-bind/qrcode/official-account
GET /store/wechat-bind/qrcode/mini-program

// 检查绑定状态
GET /store/wechat-bind/status?bindToken={token}&unionType={type}

// 获取绑定状态
GET /store/wechat-bind/bind-status

// 解除绑定
DELETE /store/wechat-bind/unbind?unionType={type}
```

#### 绑定确认API (buyer-api)

```java
// 获取绑定信息
GET /buyer/store/wechat-bind/info?token={token}&type={type}

// 确认绑定
POST /buyer/store/wechat-bind/confirm?token={token}&type={type}
```

### 2. 核心服务类

- `StoreWechatBindService` - 绑定业务逻辑
- `StoreWechatBindController` - 商家端控制器
- `StoreWechatBindPageController` - 绑定页面控制器

## 🎨 前端实现

### 1. 组件结构

```
src/views/store/wechat-bind/
├── index.vue                 # 主绑定页面
├── components/
│   ├── WechatBindPanel.vue   # 绑定面板组件
│   └── QRCodeDisplay.vue     # 二维码显示组件
└── bind-confirm.vue          # 绑定确认页面
```

### 2. 核心功能

- **二维码生成与显示**
- **状态轮询机制**
- **绑定状态管理**
- **通知开关控制**
- **解绑功能**

## 🔄 绑定流程详解

### 1. 状态机设计

```
WAITING (等待扫码)
    ↓ 用户扫码
SCANNED (已扫码)
    ↓ 用户确认
SUCCESS (绑定成功)

WAITING → EXPIRED (超时过期)
SCANNED → FAILED (绑定失败)
```

### 2. 轮询机制

- **轮询间隔**：2秒
- **超时时间**：5分钟
- **状态检查**：实时获取Redis中的状态

## 🛡️ 安全设计

### 1. 令牌安全

- 使用UUID生成唯一绑定令牌
- 令牌有效期5分钟，自动过期
- 一次性使用，绑定后失效

### 2. 权限控制

- 商家只能绑定自己的店铺
- 验证用户登录状态
- 防止重复绑定

### 3. 数据验证

- 验证店铺存在性
- 验证绑定类型合法性
- 防止恶意请求

## 📱 用户体验设计

### 1. 界面设计

- **清晰的步骤指引**
- **实时状态反馈**
- **友好的错误提示**
- **响应式设计**

### 2. 交互设计

- **一键生成二维码**
- **自动状态刷新**
- **快速绑定确认**
- **便捷解绑操作**

## 🚀 部署配置

### 1. 环境配置

```yaml
# application.yml
lili:
  domain:
    seller: http://localhost:8888  # 商家端域名
```

### 2. 依赖要求

- Redis缓存服务
- 二维码生成工具
- 微信API配置

## 🧪 测试方案

### 1. 单元测试

- 绑定服务测试
- 状态管理测试
- 安全验证测试

### 2. 集成测试

- 完整绑定流程测试
- 并发绑定测试
- 异常情况测试

### 3. 前端测试

- 组件功能测试
- 用户交互测试
- 兼容性测试

## 📈 监控与维护

### 1. 日志记录

- 绑定操作日志
- 错误异常日志
- 性能监控日志

### 2. 数据清理

- 定期清理过期令牌
- 监控缓存使用情况
- 优化查询性能

## 🔮 扩展性设计

### 1. 支持更多平台

- 支付宝绑定
- 钉钉绑定
- 其他第三方平台

### 2. 功能增强

- 批量绑定
- 绑定历史记录
- 绑定统计分析

## 📋 实施计划

1. **第一阶段**：后端API开发和测试
2. **第二阶段**：前端组件开发和集成
3. **第三阶段**：完整流程测试和优化
4. **第四阶段**：部署上线和监控

这个设计方案提供了完整的商家端微信绑定解决方案，从后端API到前端界面，从安全设计到用户体验，都进行了全面的考虑和设计。
