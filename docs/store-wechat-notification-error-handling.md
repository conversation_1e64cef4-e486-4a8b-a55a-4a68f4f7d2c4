# 商家微信通知功能错误处理修改

## 修改说明

根据您的建议，我们已经修改了商家微信通知功能中的错误处理，使用 `ResultCode` 中定义的错误码，而不是直接传递字符串给 `ResultUtil.error()` 方法。

## 新增错误码

在 `ResultCode.java` 中新增了以下错误码：

```java
/**
 * 店铺相关错误码 (50xxx)
 */
STORE_WECHAT_NOT_BIND(50013, "商家尚未绑定微信，请先在个人中心绑定微信"),
STORE_WECHAT_BIND_ERROR(50014, "商家微信绑定失败"),
STORE_WECHAT_UNBIND_ERROR(50015, "商家微信解绑失败"),
STORE_WECHAT_NOTIFICATION_UPDATE_ERROR(50016, "商家微信通知状态更新失败"),
```

## 修改的文件

### 1. ResultCode.java
- 新增了4个商家微信相关的错误码
- 错误码范围：50013-50016
- 遵循现有的错误码命名和编号规范

### 2. StoreConnectController.java
- 修改了所有 `ResultUtil.error()` 调用，使用 `ResultCode` 枚举
- 统一了返回值格式，成功时使用 `ResultUtil.data()`
- 移除了未使用的 import

## 修改前后对比

### 修改前（错误的用法）
```java
// 错误：直接传递字符串
return ResultUtil.error("商家尚未绑定微信，请先在个人中心绑定微信");
return ResultUtil.error("绑定失败");
return ResultUtil.error("绑定失败：" + e.getMessage());
```

### 修改后（正确的用法）
```java
// 正确：使用 ResultCode 枚举
return ResultUtil.error(ResultCode.STORE_WECHAT_NOT_BIND);
return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
```

## API 错误响应示例

### 商家未绑定微信
```json
{
  "success": false,
  "code": 50013,
  "message": "商家尚未绑定微信，请先在个人中心绑定微信",
  "result": null
}
```

### 绑定失败
```json
{
  "success": false,
  "code": 50014,
  "message": "商家微信绑定失败",
  "result": null
}
```

### 解绑失败
```json
{
  "success": false,
  "code": 50015,
  "message": "商家微信解绑失败",
  "result": null
}
```

### 通知状态更新失败
```json
{
  "success": false,
  "code": 50016,
  "message": "商家微信通知状态更新失败",
  "result": null
}
```

## 成功响应示例

### 绑定成功
```json
{
  "success": true,
  "code": 200,
  "message": "success",
  "result": true
}
```

### 获取绑定列表成功
```json
{
  "success": true,
  "code": 200,
  "message": "success",
  "result": [
    {
      "id": "xxx",
      "storeId": "store001",
      "storeName": "测试店铺",
      "memberId": "member001",
      "unionId": "union001",
      "unionType": "WECHAT_OFFIACCOUNT_OPEN_ID",
      "enableNotification": true,
      "createTime": "2024-12-20T10:00:00"
    }
  ]
}
```

## 优势

1. **标准化**：遵循系统统一的错误处理规范
2. **国际化支持**：错误码可以方便地支持多语言
3. **前端处理**：前端可以根据错误码进行特定的处理逻辑
4. **日志记录**：便于系统监控和问题排查
5. **API文档**：错误码使API文档更加清晰和标准

## 前端处理示例

```javascript
// 前端可以根据错误码进行特定处理
fetch('/store/seller/store/connect/bind?unionType=WECHAT_OFFIACCOUNT_OPEN_ID', {
  method: 'POST'
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('绑定成功');
  } else {
    switch (data.code) {
      case 50013:
        // 商家未绑定微信，引导用户去个人中心绑定
        showBindWechatGuide();
        break;
      case 50014:
        // 绑定失败，显示通用错误信息
        showError('绑定失败，请稍后重试');
        break;
      default:
        showError(data.message);
    }
  }
});
```

## 测试验证

所有相关的单元测试和集成测试都已更新，确保：
- 错误码正确返回
- 错误信息准确显示
- API响应格式统一
- 业务逻辑正常工作

这样的修改使得商家微信通知功能的错误处理更加规范和标准化，提升了系统的可维护性和用户体验。
