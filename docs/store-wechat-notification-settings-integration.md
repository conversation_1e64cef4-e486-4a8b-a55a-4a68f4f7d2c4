# 商家微信通知设置集成到系统设置

## 修改说明

根据您的建议，我们已经将商家微信通知设置集成到现有的系统设置框架中，而不是创建独立的Controller和Service。

## 修改内容

### 1. 添加新的设置枚举

在 `SettingEnum.java` 中添加了新的枚举值：

```java
//商家微信通知设置
STORE_WECHAT_NOTIFICATION_SETTING
```

### 2. 修改系统设置Controller

在 `SettingManagerController.java` 中：

- 在 `allowableValues` 中添加了 `STORE_WECHAT_NOTIFICATION_SETTING`
- 在 `createSetting()` 方法的switch语句中添加了对应的case处理

```java
case STORE_WECHAT_NOTIFICATION_SETTING:
    return setting == null ?
            ResultUtil.data(new StoreWechatNotificationSetting()) :
            ResultUtil.data(JSONUtil.toBean(setting.getSettingValue(), StoreWechatNotificationSetting.class));
```

### 3. 修改Service实现

修改了 `StoreWechatNotificationSettingServiceImpl.java`：

- 移除了自定义常量，改用 `SettingEnum.STORE_WECHAT_NOTIFICATION_SETTING.name()`
- 修改了保存方法以使用 `settingService.saveUpdate()` 而不是 `saveOrUpdate()`
- 确保与系统设置框架的一致性

### 4. 删除独立Controller

删除了之前创建的独立Controller：
- `StoreWechatNotificationSettingController.java`

## 新的API接口

### 获取商家微信通知设置

```http
GET /manager/setting/setting/get/STORE_WECHAT_NOTIFICATION_SETTING
```

**响应示例：**
```json
{
  "success": true,
  "result": {
    "enableStoreNotification": true,
    "enableOrderPaidNotification": true,
    "enableOrderDeliveredNotification": false,
    "enableOrderCompletedNotification": false,
    "enableOrderCancelledNotification": false,
    "notificationTimeLimit": 0,
    "notificationStartHour": 9,
    "notificationEndHour": 22
  }
}
```

### 保存商家微信通知设置

```http
PUT /manager/setting/setting/put/STORE_WECHAT_NOTIFICATION_SETTING
Content-Type: application/json

{
  "enableStoreNotification": true,
  "enableOrderPaidNotification": true,
  "enableOrderDeliveredNotification": false,
  "enableOrderCompletedNotification": false,
  "enableOrderCancelledNotification": false,
  "notificationTimeLimit": 1,
  "notificationStartHour": 9,
  "notificationEndHour": 22
}
```

## 优势

1. **一致性**：与现有系统设置框架保持一致
2. **统一管理**：所有系统设置都在同一个地方管理
3. **标准化**：遵循现有的设置管理模式
4. **维护性**：减少代码重复，便于维护

## 测试

创建了新的集成测试：
- `StoreWechatNotificationSettingIntegrationTest.java`

测试覆盖：
- 获取默认设置
- 保存和获取自定义设置
- 禁用通知的场景

## 向后兼容性

- 现有的 `StoreWechatNotificationSettingService` 接口保持不变
- 业务逻辑层的调用方式没有改变
- 只是底层存储和API接口发生了变化

## 使用示例

### 前端调用

```javascript
// 获取设置
fetch('/manager/setting/setting/get/STORE_WECHAT_NOTIFICATION_SETTING')
  .then(response => response.json())
  .then(data => {
    console.log('当前设置:', data.result);
  });

// 保存设置
const setting = {
  enableStoreNotification: true,
  enableOrderPaidNotification: true,
  enableOrderDeliveredNotification: false,
  notificationTimeLimit: 1,
  notificationStartHour: 9,
  notificationEndHour: 22
};

fetch('/manager/setting/setting/put/STORE_WECHAT_NOTIFICATION_SETTING', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(setting)
})
.then(response => response.json())
.then(data => {
  console.log('保存结果:', data.success);
});
```

### 后端调用

```java
// 获取设置
StoreWechatNotificationSetting setting = storeWechatNotificationSettingService.getStoreWechatNotificationSetting();

// 保存设置
setting.setEnableOrderPaidNotification(true);
boolean result = storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);
```

这样的修改使得商家微信通知设置完全集成到了现有的系统设置框架中，提供了更好的一致性和可维护性。
