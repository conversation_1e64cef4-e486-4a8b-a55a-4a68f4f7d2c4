# 商家微信通知功能说明

## 功能概述

商家微信通知功能允许在用户完成订单付款后，自动向产品/服务提供方（商家）发送微信通知，帮助商家及时了解新订单情况。

## 功能特性

- 支持微信公众号模板消息和小程序订阅消息
- 支持多种订单状态通知（付款、发货、完成、取消）
- 支持通知开关控制和时间限制
- 支持商家个性化通知设置
- 完整的事件驱动架构，自动触发通知

## 系统架构

### 核心组件

1. **StoreConnect** - 商家微信绑定信息管理
2. **StoreWechatMessage** - 商家微信消息模板管理
3. **StoreWechatMessageUtil** - 微信消息发送工具
4. **StoreWechatMessageExecute** - 订单事件处理器
5. **StoreWechatNotificationSetting** - 通知配置管理

### 数据库表

- `li_store_connect` - 商家微信绑定信息表
- `li_store_wechat_message` - 商家微信消息模板表
- `li_store_wechat_mp_message` - 商家微信小程序消息模板表

## 使用指南

### 1. 数据库初始化

执行以下SQL脚本创建相关表：

```sql
-- 执行 DB/store_connect.sql 中的建表语句
```

### 2. 商家微信绑定

商家需要先绑定微信账号才能接收通知：

```java
// 绑定微信公众号
storeConnectService.bindStoreWechat(storeId, unionId, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());

// 绑定微信小程序
storeConnectService.bindStoreWechat(storeId, unionId, SourceEnum.WECHAT_MP_OPEN_ID.name());
```

### 3. 配置通知设置

管理员可以配置全局通知设置：

```java
StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
setting.setEnableStoreNotification(true);
setting.setEnableOrderPaidNotification(true);
setting.setNotificationStartHour(9);
setting.setNotificationEndHour(22);
storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);
```

### 4. 初始化消息模板

系统启动时会自动初始化微信消息模板：

```java
// 初始化公众号模板
storeWechatMessageService.init();

// 初始化小程序模板
storeWechatMPMessageService.init();
```

### 5. 自动通知触发

当订单状态发生变化时，系统会自动发送通知：

- 订单付款完成 → 发送付款通知给商家
- 订单发货 → 发送发货通知给商家（可选）
- 订单完成 → 发送完成通知给商家（可选）

## API接口

### 商家端接口

#### 绑定微信通知

```
POST /store/seller/store/connect/bind?unionType=WECHAT_OFFIACCOUNT_OPEN_ID
```

#### 解绑微信通知

```
DELETE /store/seller/store/connect/unbind?unionType=WECHAT_OFFIACCOUNT_OPEN_ID
```

#### 获取绑定列表

```
GET /store/seller/store/connect/list
```

#### 更新通知状态

```
PUT /store/seller/store/connect/notification?unionType=WECHAT_OFFIACCOUNT_OPEN_ID&enable=true
```

### 管理端接口

#### 获取通知设置

```
GET /manager/system/store-wechat-notification
```

#### 保存通知设置

```
POST /manager/system/store-wechat-notification
```

## 配置说明

### 通知类型

- `ORDER_PAID` - 订单付款通知
- `ORDER_DELIVERED` - 订单发货通知
- `ORDER_COMPLETED` - 订单完成通知
- `ORDER_CANCELLED` - 订单取消通知

### 绑定类型

- `WECHAT_OFFIACCOUNT_OPEN_ID` - 微信公众号
- `WECHAT_MP_OPEN_ID` - 微信小程序

### 时间限制

可以设置通知发送的时间范围，避免在休息时间打扰商家：

- `notificationTimeLimit` - 是否启用时间限制（0=不限制，1=限制）
- `notificationStartHour` - 开始时间（24小时制）
- `notificationEndHour` - 结束时间（24小时制）

## 测试

运行单元测试和集成测试：

```bash
# 运行所有测试
mvn test

# 运行特定测试
mvn test -Dtest=StoreWechatNotificationIntegrationTest
```

## 注意事项

1. 商家必须先在个人中心绑定微信账号
2. 需要配置正确的微信公众号和小程序参数
3. 微信模板消息需要用户关注公众号
4. 小程序订阅消息需要用户授权
5. 建议在生产环境中设置合理的通知时间限制

## 故障排查

### 常见问题

1. **通知发送失败**
   - 检查商家是否绑定微信
   - 检查微信配置是否正确
   - 检查消息模板是否存在

2. **通知未触发**
   - 检查通知开关是否启用
   - 检查是否在允许的时间范围内
   - 检查订单状态是否正确

3. **绑定失败**
   - 检查商家是否已在个人中心绑定微信
   - 检查unionId是否正确

### 日志查看

相关日志标识：
- `商家微信消息发送` - 消息发送日志
- `商家微信绑定` - 绑定操作日志
- `商家微信通知配置` - 配置相关日志
