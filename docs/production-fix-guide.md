# 生产环境微信绑定问题修复指南

## 🚨 问题确认

您当前遇到的问题：
- 二维码URL：`https://store-api.dboss.pro/store-wechat-bind?token=xxx&type=xxx`
- 错误原因：URL指向了API域名，但应该指向前端域名

## 🎯 根本原因

**架构设计错误**：
1. 二维码应该指向**前端页面**，不是API接口
2. 当前配置 `lili.domain.seller` 被设置为API域名
3. 绑定确认应该是前端Vue页面，通过AJAX调用API

## ✅ 完整修复方案

### 1. 后端配置修复

**修改配置文件**：
```yaml
# application-prod.yml 或 application.yml
lili:
  domain:
    seller: https://store.dboss.pro  # 改为商家端前端域名，不是API域名
```

**当前错误配置**：
```yaml
lili:
  domain:
    seller: https://store-api.dboss.pro  # ❌ 这是API域名
```

**正确配置**：
```yaml
lili:
  domain:
    seller: https://store.dboss.pro    # ✅ 这是前端域名
```

### 2. 前端开发要求

需要在商家端Vue项目中添加绑定确认页面：

#### 路由配置
```javascript
// router/index.js
{
  path: '/store-wechat-bind',
  name: 'StoreWechatBindConfirm',
  component: () => import('@/views/store/wechat-bind/confirm.vue'),
  meta: {
    title: '微信绑定确认',
    requiresAuth: false // 允许未登录访问
  }
}
```

#### API接口
```javascript
// api/store/wechat-bind.js
export const storeWechatBindAPI = {
  // 检查绑定状态
  checkBindStatus(bindToken, unionType) {
    return request({
      url: '/store/wechat-bind/status',
      method: 'GET',
      params: { bindToken, unionType }
    })
  },

  // 确认绑定
  confirmBind(bindToken, unionType, memberId, memberName) {
    return request({
      url: '/store/wechat-bind/confirm',
      method: 'POST',
      params: { bindToken, unionType, memberId, memberName }
    })
  }
}
```

#### 绑定确认页面
```vue
<!-- views/store/wechat-bind/confirm.vue -->
<template>
  <div class="bind-confirm-page">
    <div class="confirm-container">
      <div v-if="loading" class="loading">
        <a-spin size="large" />
        <p>正在加载...</p>
      </div>
      
      <div v-else-if="bindInfo" class="bind-info">
        <a-result
          status="info"
          title="确认绑定微信通知"
          :sub-title="`绑定类型：${bindTypeText}`"
        >
          <template #extra>
            <a-space>
              <a-button type="primary" @click="confirmBind" :loading="confirming">
                确认绑定
              </a-button>
              <a-button @click="cancelBind">
                取消
              </a-button>
            </a-space>
          </template>
        </a-result>
      </div>
      
      <div v-else class="error">
        <a-result
          status="error"
          title="绑定链接已失效"
          sub-title="请重新扫码绑定"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { storeWechatBindAPI } from '@/api/store/wechat-bind'

export default {
  name: 'StoreWechatBindConfirm',
  data() {
    return {
      loading: true,
      confirming: false,
      bindInfo: null,
      token: '',
      type: ''
    }
  },
  computed: {
    bindTypeText() {
      return this.type === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序'
    }
  },
  mounted() {
    this.token = this.$route.query.token
    this.type = this.$route.query.type
    this.loadBindInfo()
  },
  methods: {
    async loadBindInfo() {
      try {
        const response = await storeWechatBindAPI.checkBindStatus(this.token, this.type)
        if (response.result === 'WAITING' || response.result === 'SCANNED') {
          this.bindInfo = { status: response.result }
        } else {
          this.bindInfo = null
        }
      } catch (error) {
        console.error('加载绑定信息失败:', error)
        this.bindInfo = null
      } finally {
        this.loading = false
      }
    },

    async confirmBind() {
      this.confirming = true
      try {
        await storeWechatBindAPI.confirmBind(this.token, this.type)
        this.$message.success('绑定成功！')
        setTimeout(() => {
          window.close()
        }, 2000)
      } catch (error) {
        this.$message.error('绑定失败，请重试')
      } finally {
        this.confirming = false
      }
    },

    cancelBind() {
      window.close()
    }
  }
}
</script>

<style scoped>
.bind-confirm-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.confirm-container {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.loading {
  text-align: center;
  padding: 40px;
}
</style>
```

### 3. 部署步骤

#### 步骤1：修改后端配置
```bash
# 1. 修改配置文件
vim /path/to/application-prod.yml

# 2. 重启store-api服务
systemctl restart store-api
# 或者
docker restart store-api-container
```

#### 步骤2：部署前端页面
```bash
# 1. 在商家端Vue项目中添加绑定确认页面
# 2. 构建前端项目
npm run build

# 3. 部署到 https://store.dboss.pro
# 确保绑定确认页面可以访问
```

#### 步骤3：验证修复
```bash
# 1. 检查二维码URL是否正确
curl -X GET "https://store-api.dboss.pro/store/wechat-bind/qrcode/mini-program" \
  -H "Authorization: Bearer your-token"

# 2. 检查前端页面是否可访问
curl https://store.dboss.pro/store-wechat-bind

# 3. 使用手机微信扫码测试
```

## 🔍 域名说明

根据您的生产环境，应该有以下域名：

| 服务 | 域名 | 用途 |
|------|------|------|
| 商家端API | `https://store-api.dboss.pro` | 后端API接口 |
| 商家端前端 | `https://store.dboss.pro` | Vue前端页面 |

**二维码应该指向前端域名！**

## ⚠️ 注意事项

### 1. 跨域配置
如果前后端域名不同，需要配置CORS：

```java
// 在store-api中配置
@CrossOrigin(origins = "https://store.dboss.pro")
```

### 2. HTTPS要求
- 微信要求使用HTTPS
- 确保SSL证书有效

### 3. 用户身份问题
绑定确认页面可能需要处理用户身份验证，可以考虑：
- 使用临时身份
- 要求用户登录
- 使用微信授权

## 🚀 快速修复

如果需要立即修复，可以：

```bash
# 临时修复：重启时指定正确域名
java -jar store-api.jar --lili.domain.seller=https://store.dboss.pro
```

修复后，二维码URL应该变为：
```
https://store.dboss.pro/store-wechat-bind?token=xxx&type=xxx
```

这样微信扫码后就可以正常访问前端绑定页面了！
