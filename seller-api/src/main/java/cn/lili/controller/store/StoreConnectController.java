package cn.lili.controller.store;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.entity.Connect;
import cn.lili.modules.connect.service.ConnectService;
import cn.lili.modules.store.entity.dos.StoreConnect;
import cn.lili.modules.store.service.StoreConnectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商家微信绑定管理API
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@RestController
@Api(tags = "商家微信绑定管理API")
@RequestMapping("/store/seller/store/connect")
public class StoreConnectController {

    @Autowired
    private StoreConnectService storeConnectService;

    @Autowired
    private ConnectService connectService;

    @ApiOperation(value = "绑定商家微信")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query")
    })
    @PostMapping("/bind")
    public ResultMessage<Boolean> bindWechat(@RequestParam String unionType) {
        try {
            // 获取当前登录商家信息
            String storeId = UserContext.getCurrentUser().getStoreId();
            String memberId = UserContext.getCurrentUser().getId();
            
            // 从Connect表中获取商家的微信绑定信息
            Connect connect = connectService.queryConnect(memberId, null, unionType);
            if (connect == null) {
                return ResultUtil.error(ResultCode.STORE_WECHAT_NOT_BIND);
            }

            // 绑定商家微信通知
            boolean result = storeConnectService.bindStoreWechat(storeId, connect.getUnionId(), unionType);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
            }
        } catch (Exception e) {
            log.error("绑定商家微信失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
        }
    }

    @ApiOperation(value = "解绑商家微信")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query")
    })
    @DeleteMapping("/unbind")
    public ResultMessage<Boolean> unbindWechat(@RequestParam String unionType) {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            boolean result = storeConnectService.unbindStoreWechat(storeId, unionType);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_UNBIND_ERROR);
            }
        } catch (Exception e) {
            log.error("解绑商家微信失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_UNBIND_ERROR);
        }
    }

    @ApiOperation(value = "获取商家微信绑定列表")
    @GetMapping("/list")
    public ResultMessage<List<StoreConnect>> getBindList() {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            List<StoreConnect> list = storeConnectService.getByStoreId(storeId);
            return ResultUtil.data(list);
        } catch (Exception e) {
            log.error("获取商家微信绑定列表失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "更新微信通知状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "enable", value = "是否启用", required = true, dataType = "Boolean", paramType = "query")
    })
    @PutMapping("/notification")
    public ResultMessage<Boolean> updateNotificationStatus(@RequestParam String unionType, 
                                                          @RequestParam Boolean enable) {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            boolean result = storeConnectService.updateNotificationStatus(storeId, unionType, enable);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_NOTIFICATION_UPDATE_ERROR);
            }
        } catch (Exception e) {
            log.error("更新微信通知状态失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_NOTIFICATION_UPDATE_ERROR);
        }
    }
}
