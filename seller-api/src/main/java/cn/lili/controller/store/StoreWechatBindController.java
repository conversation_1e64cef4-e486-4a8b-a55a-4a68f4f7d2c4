package cn.lili.controller.store;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.entity.Connect;
import cn.lili.modules.connect.entity.dto.ConnectAuthUser;
import cn.lili.modules.connect.entity.enums.SourceEnum;
import cn.lili.modules.connect.service.ConnectService;
import cn.lili.modules.store.entity.dos.StoreConnect;
import cn.lili.modules.store.entity.dto.StoreWechatBindResult;
import cn.lili.modules.store.service.StoreConnectService;
import cn.lili.modules.store.service.StoreWechatBindService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商家微信绑定API - 提供扫码绑定功能
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@RestController
@Api(tags = "商家微信绑定API")
@RequestMapping("/store/seller/store/wechat-bind")
public class StoreWechatBindController {

    @Autowired
    private StoreWechatBindService storeWechatBindService;

    @Autowired
    private StoreConnectService storeConnectService;

    @Autowired
    private ConnectService connectService;

    @ApiOperation(value = "生成微信公众号绑定二维码")
    @GetMapping("/qrcode/official-account")
    public ResultMessage<StoreWechatBindResult> generateOfficialAccountQRCode() {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            String memberId = UserContext.getCurrentUser().getId();
            
            StoreWechatBindResult result = storeWechatBindService.generateWechatBindQRCode(
                    storeId, memberId, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
            
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("生成微信公众号绑定二维码失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "生成微信小程序绑定二维码")
    @GetMapping("/qrcode/mini-program")
    public ResultMessage<StoreWechatBindResult> generateMiniProgramQRCode() {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            String memberId = UserContext.getCurrentUser().getId();
            
            StoreWechatBindResult result = storeWechatBindService.generateWechatBindQRCode(
                    storeId, memberId, SourceEnum.WECHAT_MP_OPEN_ID.name());
            
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("生成微信小程序绑定二维码失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "检查绑定状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bindToken", value = "绑定令牌", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/status")
    public ResultMessage<String> checkBindStatus(@RequestParam String bindToken, @RequestParam String unionType) {
        try {
            String status = storeWechatBindService.checkBindStatus(bindToken, unionType);
            return ResultUtil.data(status);
        } catch (Exception e) {
            log.error("检查绑定状态失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "微信扫码绑定回调")
    @PostMapping("/callback")
    public ResultMessage<Boolean> wechatBindCallback(@RequestBody ConnectAuthUser authUser, 
                                                   @RequestParam String bindToken,
                                                   @RequestParam String unionType) {
        try {
            boolean result = storeWechatBindService.processWechatBind(authUser, bindToken, unionType);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
            }
        } catch (Exception e) {
            log.error("微信扫码绑定回调失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
        }
    }

    @ApiOperation(value = "直接绑定微信（如果已有Connect记录）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query")
    })
    @PostMapping("/direct-bind")
    public ResultMessage<Boolean> directBindWechat(@RequestParam String unionType) {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            String memberId = UserContext.getCurrentUser().getId();
            
            // 从Connect表中获取商家的微信绑定信息
            Connect connect = connectService.queryConnect(memberId, null, unionType);
            if (connect == null) {
                return ResultUtil.error(ResultCode.STORE_WECHAT_NOT_BIND);
            }

            // 绑定商家微信通知
            boolean result = storeConnectService.bindStoreWechat(storeId, connect.getUnionId(), unionType);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
            }
        } catch (Exception e) {
            log.error("直接绑定微信失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
        }
    }

    @ApiOperation(value = "获取商家微信绑定状态")
    @GetMapping("/bind-status")
    public ResultMessage<List<StoreConnect>> getBindStatus() {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            List<StoreConnect> list = storeConnectService.getByStoreId(storeId);
            return ResultUtil.data(list);
        } catch (Exception e) {
            log.error("获取商家微信绑定状态失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "解绑商家微信")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query")
    })
    @DeleteMapping("/unbind")
    public ResultMessage<Boolean> unbindWechat(@RequestParam String unionType) {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            boolean result = storeConnectService.unbindStoreWechat(storeId, unionType);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_UNBIND_ERROR);
            }
        } catch (Exception e) {
            log.error("解绑商家微信失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_UNBIND_ERROR);
        }
    }
}
