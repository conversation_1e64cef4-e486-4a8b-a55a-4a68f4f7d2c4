package cn.lili.controller.store;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.entity.Connect;
import cn.lili.modules.connect.entity.dto.ConnectAuthUser;
import cn.lili.modules.connect.entity.enums.SourceEnum;
import cn.lili.modules.connect.service.ConnectService;
import cn.lili.modules.store.entity.dos.StoreConnect;
import cn.lili.modules.store.entity.dto.StoreWechatBindResult;
import cn.lili.modules.store.service.StoreConnectService;
import cn.lili.modules.store.service.StoreWechatBindService;
import cn.lili.common.utils.HttpUtils;
import cn.lili.modules.system.entity.dto.connect.dto.WechatConnectSettingItem;
import cn.lili.modules.system.entity.dto.connect.ConnectSettingDTO;
import cn.lili.modules.system.service.SettingService;
import cn.lili.modules.system.entity.enums.SettingEnum;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

import java.util.List;

/**
 * 商家微信绑定API - 提供扫码绑定功能
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@RestController
@Api(tags = "商家微信绑定API")
@RequestMapping("/store/wechat-bind")
public class StoreWechatBindController {

    @Autowired
    private StoreWechatBindService storeWechatBindService;

    @Autowired
    private StoreConnectService storeConnectService;

    @Autowired
    private ConnectService connectService;

    @Autowired
    private SettingService settingService;

    @ApiOperation(value = "生成微信公众号绑定二维码")
    @GetMapping("/qrcode/official-account")
    public ResultMessage<StoreWechatBindResult> generateOfficialAccountQRCode() {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            String memberId = UserContext.getCurrentUser().getId();
            
            StoreWechatBindResult result = storeWechatBindService.generateWechatBindQRCode(
                    storeId, memberId, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
            
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("生成微信公众号绑定二维码失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "生成微信小程序绑定二维码")
    @GetMapping("/qrcode/mini-program")
    public ResultMessage<StoreWechatBindResult> generateMiniProgramQRCode() {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            String memberId = UserContext.getCurrentUser().getId();
            
            StoreWechatBindResult result = storeWechatBindService.generateWechatBindQRCode(
                    storeId, memberId, SourceEnum.WECHAT_MP_OPEN_ID.name());
            
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("生成微信小程序绑定二维码失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "检查绑定状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bindToken", value = "绑定令牌", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/status")
    public ResultMessage<String> checkBindStatus(@RequestParam String bindToken, @RequestParam String unionType) {
        try {
            String status = storeWechatBindService.checkBindStatus(bindToken, unionType);
            return ResultUtil.data(status);
        } catch (Exception e) {
            log.error("检查绑定状态失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "确认绑定（供前端绑定页面调用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bindToken", value = "绑定令牌", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "unionId", value = "微信unionId", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "nickname", value = "微信昵称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "avatar", value = "微信头像", required = false, dataType = "String", paramType = "query")
    })
    @PostMapping("/confirm")
    public ResultMessage<Boolean> confirmBind(@RequestParam String bindToken,
                                            @RequestParam String unionType,
                                            @RequestParam String unionId,
                                            @RequestParam(required = false) String nickname,
                                            @RequestParam(required = false) String avatar) {
        try {
            // 验证必要参数
            if (unionId == null || unionId.trim().isEmpty()) {
                log.error("确认绑定失败：unionId不能为空");
                return ResultUtil.error(ResultCode.PARAMS_ERROR);
            }

            // 构建微信用户信息
            ConnectAuthUser authUser = new ConnectAuthUser();
            authUser.setUuid(unionId.trim());  // 使用真实的unionId
            authUser.setUsername(nickname != null ? nickname : "微信用户");
            authUser.setNickname(nickname != null ? nickname : "微信用户");
            authUser.setAvatar(avatar);

            boolean result = storeWechatBindService.processWechatBind(authUser, bindToken, unionType);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
            }
        } catch (Exception e) {
            log.error("确认绑定失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
        }
    }

    @ApiOperation(value = "直接绑定微信（如果已有Connect记录）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query")
    })
    @PostMapping("/direct-bind")
    public ResultMessage<Boolean> directBindWechat(@RequestParam String unionType) {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            String memberId = UserContext.getCurrentUser().getId();
            
            // 从Connect表中获取商家的微信绑定信息
            Connect connect = connectService.queryConnect(memberId, null, unionType);
            if (connect == null) {
                return ResultUtil.error(ResultCode.STORE_WECHAT_NOT_BIND);
            }

            // 绑定商家微信通知
            boolean result = storeConnectService.bindStoreWechat(storeId, connect.getUnionId(), unionType);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
            }
        } catch (Exception e) {
            log.error("直接绑定微信失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_BIND_ERROR);
        }
    }

    @ApiOperation(value = "获取商家微信绑定状态")
    @GetMapping("/bind-status")
    public ResultMessage<List<StoreConnect>> getBindStatus() {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            List<StoreConnect> list = storeConnectService.getByStoreId(storeId);
            return ResultUtil.data(list);
        } catch (Exception e) {
            log.error("获取商家微信绑定状态失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @ApiOperation(value = "解绑商家微信")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "unionType", value = "绑定类型", required = true, dataType = "String", paramType = "query")
    })
    @DeleteMapping("/unbind")
    public ResultMessage<Boolean> unbindWechat(@RequestParam String unionType) {
        try {
            String storeId = UserContext.getCurrentUser().getStoreId();
            boolean result = storeConnectService.unbindStoreWechat(storeId, unionType);
            if (result) {
                return ResultUtil.data(true);
            } else {
                return ResultUtil.error(ResultCode.STORE_WECHAT_UNBIND_ERROR);
            }
        } catch (Exception e) {
            log.error("解绑商家微信失败", e);
            return ResultUtil.error(ResultCode.STORE_WECHAT_UNBIND_ERROR);
        }
    }

    @ApiOperation(value = "通过微信授权码获取用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信授权码", required = true, dataType = "String", paramType = "query")
    })
    @GetMapping("/getUserInfo")
    public ResultMessage<Map<String, Object>> getUserInfo(@RequestParam String code) {
        try {
            // 获取微信配置
            WechatConnectSettingItem wechatSetting = getWechatSetting();
            if (wechatSetting == null) {
                return ResultUtil.error(ResultCode.ERROR);
            }

            // 1. 通过code获取access_token
            String accessTokenUrl = String.format(
                    "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
                    wechatSetting.getAppId(), wechatSetting.getAppSecret(), code);

            String tokenResponse = HttpUtils.doGet(accessTokenUrl, "UTF-8", 10000, 10000);
            JSONObject tokenJson = JSONObject.parseObject(tokenResponse);

            // 检查是否有错误
            if (tokenJson.containsKey("errcode")) {
                log.error("获取access_token失败: {}", tokenResponse);
                return ResultUtil.error(ResultCode.ERROR);
            }

            String accessToken = tokenJson.getString("access_token");
            String openId = tokenJson.getString("openid");

            // 2. 通过access_token获取用户信息
            String userInfoUrl = String.format(
                    "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
                    accessToken, openId);

            String userInfoResponse = HttpUtils.doGet(userInfoUrl, "UTF-8", 10000, 10000);
            JSONObject userInfoJson = JSONObject.parseObject(userInfoResponse);

            // 检查是否有错误
            if (userInfoJson.containsKey("errcode")) {
                log.error("获取用户信息失败: {}", userInfoResponse);
                return ResultUtil.error(ResultCode.ERROR);
            }

            // 3. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("openid", userInfoJson.getString("openid"));
            result.put("unionid", userInfoJson.getString("unionid"));
            result.put("nickname", userInfoJson.getString("nickname"));
            result.put("headimgurl", userInfoJson.getString("headimgurl"));
            result.put("sex", userInfoJson.getInteger("sex"));
            result.put("city", userInfoJson.getString("city"));
            result.put("province", userInfoJson.getString("province"));
            result.put("country", userInfoJson.getString("country"));

            return ResultUtil.data(result);

        } catch (Exception e) {
            log.error("获取微信用户信息失败", e);
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    /**
     * 获取微信配置
     *
     * @return 微信配置
     */
    private WechatConnectSettingItem getWechatSetting() {
        try {
            // 简化实现，使用固定配置或从环境变量获取
            WechatConnectSettingItem wechatSetting = new WechatConnectSettingItem();
            // 这里应该从配置中获取，暂时返回null让调用方处理
            // 实际部署时需要配置正确的appId和appSecret
            return null; // 需要在实际使用时配置微信应用信息
        } catch (Exception e) {
            log.error("获取微信配置失败", e);
        }
        return null;
    }
}
