-- 商家微信绑定信息表
CREATE TABLE `li_store_connect` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `delete_flag` bit(1) DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `store_id` varchar(32) NOT NULL COMMENT '店铺ID',
  `store_name` varchar(100) DEFAULT NULL COMMENT '店铺名称',
  `member_id` varchar(32) NOT NULL COMMENT '商家会员ID',
  `union_id` varchar(100) NOT NULL COMMENT '微信unionId',
  `union_type` varchar(50) NOT NULL COMMENT '绑定类型：WECHAT_OFFIACCOUNT_OPEN_ID-公众号，WECHAT_MP_OPEN_ID-小程序',
  `enable_notification` bit(1) DEFAULT b'1' COMMENT '是否启用微信通知',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_store_union_type` (`store_id`, `union_type`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_union_id` (`union_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='商家微信绑定信息表';

-- 商家微信消息模板表
CREATE TABLE `li_store_wechat_message` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `delete_flag` bit(1) DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `code` varchar(100) NOT NULL COMMENT '模板编码',
  `first` varchar(200) DEFAULT NULL COMMENT '模板首部内容',
  `remark` varchar(200) DEFAULT NULL COMMENT '模板尾部内容',
  `keywords` varchar(500) DEFAULT NULL COMMENT '模板关键字',
  `order_status` varchar(50) NOT NULL COMMENT '订单状态',
  `enable` bit(1) DEFAULT b'1' COMMENT '是否启用',
  `message_type` varchar(50) NOT NULL COMMENT '消息类型：ORDER_PAID-订单付款，ORDER_DELIVERED-订单发货',
  PRIMARY KEY (`id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_message_type` (`message_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='商家微信消息模板表';

-- 商家微信小程序消息模板表
CREATE TABLE `li_store_wechat_mp_message` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `delete_flag` bit(1) DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `code` varchar(100) NOT NULL COMMENT '模板编码',
  `keywords` varchar(500) DEFAULT NULL COMMENT '模板关键字',
  `keywords_text` varchar(500) DEFAULT NULL COMMENT '模板关键字文本',
  `order_status` varchar(50) NOT NULL COMMENT '订单状态',
  `enable` bit(1) DEFAULT b'1' COMMENT '是否启用',
  `message_type` varchar(50) NOT NULL COMMENT '消息类型：ORDER_PAID-订单付款，ORDER_DELIVERED-订单发货',
  `scene_desc` varchar(200) DEFAULT NULL COMMENT '场景描述',
  PRIMARY KEY (`id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_message_type` (`message_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='商家微信小程序消息模板表';
