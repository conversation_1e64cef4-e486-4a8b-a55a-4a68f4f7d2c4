package cn.lili.common.utils;

import cn.lili.modules.payment.kit.core.kit.QrCodeKit;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码生成工具类
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
public class QRCodeUtil {

    /**
     * 默认二维码宽度
     */
    private static final int DEFAULT_WIDTH = 300;

    /**
     * 默认二维码高度
     */
    private static final int DEFAULT_HEIGHT = 300;

    /**
     * 默认边距
     */
    private static final int DEFAULT_MARGIN = 1;

    /**
     * 生成二维码并返回Base64编码的图片
     *
     * @param content 二维码内容
     * @return Base64编码的PNG图片
     */
    public static String createQRCode(String content) {
        return createQRCode(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成二维码并返回Base64编码的图片
     *
     * @param content 二维码内容
     * @param width   宽度
     * @param height  高度
     * @return Base64编码的PNG图片
     */
    public static String createQRCode(String content, int width, int height) {
        return createQRCode(content, width, height, DEFAULT_MARGIN);
    }

    /**
     * 生成二维码并返回Base64编码的图片
     *
     * @param content 二维码内容
     * @param width   宽度
     * @param height  高度
     * @param margin  边距
     * @return Base64编码的PNG图片
     */
    public static String createQRCode(String content, int width, int height, int margin) {
        try {
            // 设置二维码参数
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.MARGIN, margin);

            // 生成二维码矩阵
            BitMatrix bitMatrix = new MultiFormatWriter().encode(
                    content, BarcodeFormat.QR_CODE, width, height, hints);

            // 配置图片颜色
            MatrixToImageConfig config = new MatrixToImageConfig(0xFF000001, 0xFFFFFFFF);

            // 生成BufferedImage
            BufferedImage bufferedImage = MatrixToImageWriter.toBufferedImage(bitMatrix, config);

            // 转换为Base64
            return bufferedImageToBase64(bufferedImage);

        } catch (WriterException | IOException e) {
            log.error("生成二维码失败", e);
            throw new RuntimeException("生成二维码失败", e);
        }
    }

    /**
     * 生成二维码并保存到文件
     *
     * @param content  二维码内容
     * @param filePath 文件路径
     * @return 是否成功
     */
    public static boolean createQRCodeToFile(String content, String filePath) {
        return createQRCodeToFile(content, DEFAULT_WIDTH, DEFAULT_HEIGHT, filePath);
    }

    /**
     * 生成二维码并保存到文件
     *
     * @param content  二维码内容
     * @param width    宽度
     * @param height   高度
     * @param filePath 文件路径
     * @return 是否成功
     */
    public static boolean createQRCodeToFile(String content, int width, int height, String filePath) {
        return QrCodeKit.encode(content, BarcodeFormat.QR_CODE, DEFAULT_MARGIN,
                ErrorCorrectionLevel.M, "PNG", width, height, filePath);
    }

    /**
     * 将BufferedImage转换为Base64编码的字符串
     *
     * @param bufferedImage BufferedImage对象
     * @return Base64编码的字符串
     * @throws IOException IO异常
     */
    private static String bufferedImageToBase64(BufferedImage bufferedImage) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage, "PNG", baos);
        byte[] bytes = baos.toByteArray();
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 生成二维码字节数组
     *
     * @param content 二维码内容
     * @return 二维码图片字节数组
     */
    public static byte[] createQRCodeBytes(String content) {
        return createQRCodeBytes(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成二维码字节数组
     *
     * @param content 二维码内容
     * @param width   宽度
     * @param height  高度
     * @return 二维码图片字节数组
     */
    public static byte[] createQRCodeBytes(String content, int width, int height) {
        try {
            // 设置二维码参数
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.MARGIN, DEFAULT_MARGIN);

            // 生成二维码矩阵
            BitMatrix bitMatrix = new MultiFormatWriter().encode(
                    content, BarcodeFormat.QR_CODE, width, height, hints);

            // 配置图片颜色
            MatrixToImageConfig config = new MatrixToImageConfig(0xFF000001, 0xFFFFFFFF);

            // 生成BufferedImage
            BufferedImage bufferedImage = MatrixToImageWriter.toBufferedImage(bitMatrix, config);

            // 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "PNG", baos);
            return baos.toByteArray();

        } catch (WriterException | IOException e) {
            log.error("生成二维码字节数组失败", e);
            throw new RuntimeException("生成二维码字节数组失败", e);
        }
    }
}