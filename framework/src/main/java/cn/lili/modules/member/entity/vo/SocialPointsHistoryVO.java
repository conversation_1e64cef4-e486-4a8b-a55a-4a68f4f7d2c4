package cn.lili.modules.member.entity.vo;


import cn.lili.common.security.sensitive.Sensitive;
import cn.lili.common.security.sensitive.enums.SensitiveStrategy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 会员积分历史
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
public class SocialPointsHistoryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "变动前积分")
    private String oldremain;


    @Sensitive(strategy = SensitiveStrategy.PHONE)
    @ApiModelProperty(value = "变动积分")
    private String change;

    @ApiModelProperty(value = "当前积分")
    private String remain;

    @ApiModelProperty(value = "积分变动原因")
    private String reason;

}