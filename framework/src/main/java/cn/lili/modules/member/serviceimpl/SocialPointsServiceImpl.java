package cn.lili.modules.member.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.connect.service.ConnectService;
import cn.lili.modules.member.entity.vo.MemberPointsHistoryVO;
import cn.lili.modules.member.entity.vo.SocialPointsHistoryVO;
import cn.lili.modules.member.entity.vo.SocialPointsVO;
import cn.lili.modules.member.service.SocialPointsService;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * 会员积分历史业务层实现
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Service
@Slf4j
public class SocialPointsServiceImpl implements SocialPointsService {

    // 返回的JSON数据中，积分变动记录的key
    private static final String KEY_DATA = "data";
    private static final String KEY_POINT = "point";
    private static final String KEY_FREEZE_POINT = "pointfreeze";
    private static final String KEY_OLDREMAIN = "oldremain";
    private static final String KEY_CHANGE = "change";
    private static final String KEY_REMAIN = "remain";
    private static final String KEY_REASON = "reason";

    @Autowired
    private ConnectService connectService;

    // 社交生态积分API BASE URL
    private static final String SCOIAL_POINT_BASE_URL = "https://platform.wumengyoupin.com/open/api/user/userpoint/mypoint";
    private static final String SCOIAL_POINT_UPDATE_URL = "https://platform.wumengyoupin.com/open/api/user/userpoint/updatepoint";
    private static final String SCOIAL_POINT_PAGE_BASE_URL = "https://platform.wumengyoupin.com/open/api/user/userpoint/pageusagepoint";
    private static final String APP_TOKEN = "f730b1a19581d04c45fad9adbc925350";

    @Override
    public MemberPointsHistoryVO getMemberPointsHistoryVOByWxUnionId(String wxUnionId) {
        // 请求地址-正式地址
        String reqURL = UriComponentsBuilder.fromHttpUrl(SCOIAL_POINT_BASE_URL).queryParam("wxopenid", wxUnionId)
                .toUriString();
        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("wxopenid", wxUnionId);

        // 构建请求头，添加apptoken
        Map<String, String> headers = new HashMap<>();
        headers.put("apptoken", APP_TOKEN);

        try {
            // 发送POST请求
            String result = sendGet(reqURL, params, headers);
            // 解析返回的JSON字符串
            JSONObject jsonObject = new JSONObject(result);
            if (!jsonObject.has(KEY_DATA)) {
                throw new IllegalArgumentException("Missing required fields in 'data'");
            }
            int code = jsonObject.getInt("code");
            if (code == 0) {
                JSONObject data = jsonObject.getJSONObject(KEY_DATA);
                MemberPointsHistoryVO memberPointsHistoryVO = new MemberPointsHistoryVO();
                // 格式化 point 和 freezePoint 为两位小数
                String point = new BigDecimal(data.optString(KEY_POINT, "0")).setScale(2, BigDecimal.ROUND_HALF_UP)
                        .toString();
                String freezePoint = new BigDecimal(data.optString(KEY_FREEZE_POINT, "0"))
                        .setScale(2, BigDecimal.ROUND_HALF_UP).toString();

                memberPointsHistoryVO.setPoint(point);
                memberPointsHistoryVO.setFreezePoint(freezePoint);
                return memberPointsHistoryVO;
            } else {
                // 处理错误码
                String msg = jsonObject.getString("msg");
                log.error("获取用户积分失败，错误码：" + msg);
                throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
            }
        } catch (Exception e) {
            // 处理网络请求异常
            log.error("获取用户积分失败，错误码：" + e.getMessage());
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }

    }

    @Override
    public MemberPointsHistoryVO getCurrentUserPoints() {
        AuthUser authUser = UserContext.getCurrentUser();
        if (authUser == null) {
            log.error("获取用户积分失败，用户未登录");
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }
        String wxUnionId = connectService.getUnionIdByMemberId(authUser.getId());
        if (CharSequenceUtil.isEmpty(wxUnionId)) {
            log.error("获取用户积分失败，用户未绑定微信");
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }
        return getMemberPointsHistoryVOByWxUnionId(wxUnionId);

    }

    @Override
    public MemberPointsHistoryVO updateCurrentUserPoints(SocialPointsVO socialPoints) {
        AuthUser authUser = UserContext.getCurrentUser();
        if (authUser == null) {
            log.error("更新用户积分失败，用户未登录");
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }
        String wxUnionId = connectService.getUnionIdByMemberId(authUser.getId());
        if (CharSequenceUtil.isEmpty(wxUnionId)) {
            log.error("更新用户积分失败，用户未绑定微信");
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }
        socialPoints.setWxUnionId(wxUnionId);
        return updateCurrentUserPoints(socialPoints);

    }

    @Override
    public IPage<SocialPointsHistoryVO> getMemberPointsHistoryByWx(PageVO page, String wxUnionId) {
        // 请求地址-正式地址
        String reqURL = UriComponentsBuilder.fromHttpUrl(SCOIAL_POINT_PAGE_BASE_URL).queryParam("wxopenid", wxUnionId)
                .queryParam("page", page.getPageNumber()).queryParam("limit", page.getPageSize()).toUriString();
        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("wxopenid", wxUnionId);
        params.put("page", String.valueOf(page.getPageNumber()));
        params.put("limit", String.valueOf(page.getPageSize()));

        // 构建请求头，添加apptoken
        Map<String, String> headers = new HashMap<>();
        headers.put("apptoken", APP_TOKEN);

        try {
            // 发送POST请求
            String result = sendPost(reqURL, params, headers);
            // 解析返回的JSON字符串
            JSONObject jsonObject = new JSONObject(result);
            if (!jsonObject.has(KEY_DATA)) {
                throw new IllegalArgumentException("Missing required fields in 'data'");
            }

            int code = jsonObject.getInt("code");
            if (code == 0) {
                int count = jsonObject.getInt("count");
                int totalPage = jsonObject.getInt("totalPage");
                JSONArray data = jsonObject.getJSONArray(KEY_DATA); // 使用getJSONArray而不是getJSONObject
                // 将 JSONArray 转换为 List<SocialPointsHistoryVO>
                List<SocialPointsHistoryVO> historyList = IntStream.range(0, data.length()).mapToObj(index -> {
                    try {
                        JSONObject itemJson = data.getJSONObject(index); // 从JSONArray中获取JSONObject
                        SocialPointsHistoryVO vo = new SocialPointsHistoryVO();
                        vo.setOldremain(itemJson.optString(KEY_OLDREMAIN, "0.00"));
                        vo.setChange(itemJson.optString(KEY_CHANGE, "0.00"));
                        vo.setRemain(itemJson.optString(KEY_REMAIN, "0.00"));
                        vo.setReason(itemJson.optString(KEY_REASON, ""));
                        return vo;
                    } catch (JSONException e) {
                        log.error("解析社交积分历史记录JSON时出错: {}", e.getMessage());
                        throw new ServiceException(ResultCode.USER_POINTS_QUERY_JSON_PARSE_ERROR);
                    }
                }).collect(Collectors.toList());

                // 封装分页信息
                IPage<SocialPointsHistoryVO> resultPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(
                        page.getPageNumber(), page.getPageSize(), count);
                resultPage.setRecords(historyList);
                resultPage.setPages(totalPage);
                return resultPage;
            } else {
                // 处理错误响应
                throw new RuntimeException("API调用失败: " + jsonObject.getInt("code"));
            }
        } catch (JSONException e) {
            log.error("用户社交积分查询JSON解析异常: {}", e.getMessage());
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_JSON_PARSE_ERROR);
        } catch (Exception e) {
            log.error("获取用户积分失败，错误码：{}", e.getMessage());
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }
    }

    @Override
    public IPage<SocialPointsHistoryVO> getCurrentUserPointsHistory(PageVO page) {
        AuthUser authUser = UserContext.getCurrentUser();
        if (authUser == null) {
            log.error("获取用户积分失败，用户未登录");
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }
        String wxUnionId = connectService.getUnionIdByMemberId(authUser.getId());
        if (CharSequenceUtil.isEmpty(wxUnionId)) {
            log.error("获取用户积分失败，用户未绑定微信");
            throw new ServiceException(ResultCode.USER_POINTS_QUERY_ERROR);
        }
        return getMemberPointsHistoryByWx(page, wxUnionId);

    }

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url     发送请求的 URL
     * @param params  请求的参数集合
     * @param headers 请求头参数集合
     * @return 远程资源的响应结果
     */
    @SuppressWarnings("unused")
    private String sendGet(String url, Map<String, String> params, Map<String, String> headers) {
        StringBuilder result = new StringBuilder();
        try {
            // 构建带参数的URL
            String paramString = buildQueryParams(params);
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();

            // 设置请求方法为GET
            conn.setRequestMethod("GET");
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

            // 设置额外的请求头
            if (headers != null) {
                Set<Map.Entry<String, String>> entrySet = headers.entrySet();
                for (Map.Entry<String, String> entry : entrySet) {
                    conn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }

            // 连接
            conn.connect();

            // 定义BufferedReader输入流来读取URL的响应
            try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                String line;
                while ((line = in.readLine()) != null) {
                    result.append(line);
                }
            }
        } catch (Exception e) {
            log.error("向指定 URL 发送GET方法的请求错误", e);
        }
        return result.toString();
    }

    @Override
    public MemberPointsHistoryVO updatePointsByWxUnionId(SocialPointsVO socialPoints) {

        // 使用UriComponentsBuilder构建请求URL
        String reqURL = UriComponentsBuilder.fromHttpUrl(SCOIAL_POINT_UPDATE_URL)
                .queryParam("wxopenid", socialPoints.getWxUnionId()).toUriString();
        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("wxopenid", socialPoints.getWxUnionId());
        params.put("change", socialPoints.getChangePoint().toString());
        params.put("reason", socialPoints.getReason());

        // 构建请求头，添加apptoken
        Map<String, String> headers = new HashMap<>();
        headers.put("apptoken", APP_TOKEN);

        try {
            // 发送POST请求
            String result = sendPost(reqURL, params, headers);
            // 解析返回的JSON字符串
            JSONObject jsonObject = new JSONObject(result);
            int code = jsonObject.getInt("code");
            if (code == 0) {
                JSONObject data = jsonObject.getJSONObject("data");
                MemberPointsHistoryVO memberPointsHistoryVO = new MemberPointsHistoryVO();
                memberPointsHistoryVO.setPoint(data.optString("point", "0.00"));
                memberPointsHistoryVO.setFreezePoint(data.optString("pointfreeze", "0.00"));
                return memberPointsHistoryVO;
            } else {
                // 处理错误码
                String msg = jsonObject.getString("msg");
                return new MemberPointsHistoryVO();
            }
        } catch (Exception e) {
            // 处理网络请求异常
            return new MemberPointsHistoryVO();
        }

    }

    /**
     * 构建查询参数字符串
     *
     * @param params 参数集合
     * @return 查询参数字符串
     */
    private String buildQueryParams(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        return params.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue())
                .reduce((a, b) -> a + "&" + b).orElse("");
    }

    private String sendPost(String url, Map<String, String> params, Map<String, String> headers) {
        StringBuilder result = new StringBuilder();
        try {
            // 构建带参数的POST请求体
            String paramString = buildQueryParams(params);
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();

            // 设置请求方法为POST
            conn.setRequestMethod("POST");
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

            // 设置额外的请求头
            if (headers != null) {
                Set<Map.Entry<String, String>> entrySet = headers.entrySet();
                for (Map.Entry<String, String> entry : entrySet) {
                    conn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }

            // 设置是否向HttpURLConnection输出，因为POST需要提交内容
            conn.setDoOutput(true);
            conn.setDoInput(true);

            // 连接
            conn.connect();

            // 写入POST请求的参数
            try (OutputStream os = conn.getOutputStream()) {
                os.write(paramString.getBytes(StandardCharsets.UTF_8));
                os.flush();
            }

            // 定义BufferedReader输入流来读取URL的响应
            try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                String line;
                while ((line = in.readLine()) != null) {
                    result.append(line);
                }
            }
        } catch (Exception e) {
            log.error("向指定 URL 发送POST方法的请求错误", e);
        }
        return result.toString();
    }

}