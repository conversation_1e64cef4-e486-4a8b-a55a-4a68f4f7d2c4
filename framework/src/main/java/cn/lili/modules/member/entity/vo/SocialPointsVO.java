package cn.lili.modules.member.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员积分VO
 *
 * <AUTHOR>
 * @since 2021/2/25 9:52 上午
 */
@Data
public class SocialPointsVO {

    @ApiModelProperty(value = "用户微信unionId")
    private String wxUnionId;

    @ApiModelProperty(value = "当前会员积分")
    private String point;

    @ApiModelProperty(value = "变动积分")
    private String changePoint;

    @ApiModelProperty(value = "累计获得积分")
    private String totalPoint;    

    @ApiModelProperty(value = "变动原因")
    private String reason;

    @ApiModelProperty(value = "冻结积分")
    private String freezePoint;

    public SocialPointsVO(){
        this.point = "";
        this.changePoint = "";
        this.freezePoint = "";
        this.totalPoint = "";
    }
}
