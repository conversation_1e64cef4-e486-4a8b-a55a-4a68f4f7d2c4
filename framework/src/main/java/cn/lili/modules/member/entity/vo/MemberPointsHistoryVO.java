package cn.lili.modules.member.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员积分VO
 *
 * <AUTHOR>
 * @since 2021/2/25 9:52 上午
 */
@Data
public class MemberPointsHistoryVO {

    @ApiModelProperty(value = "当前会员积分")
    private String point;

    @ApiModelProperty(value = "累计获得积分")
    private String totalPoint;

    @ApiModelProperty(value = "冻结积分")
    private String freezePoint;


    public MemberPointsHistoryVO(){
        this.point = "0.00";
        this.freezePoint = "0.00";
        this.totalPoint = "0.00";
    }
}
