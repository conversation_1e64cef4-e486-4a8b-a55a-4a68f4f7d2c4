package cn.lili.modules.wechat.serviceimpl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ClientTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.HttpUtils;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.wechat.entity.dos.StoreWechatMessage;
import cn.lili.modules.wechat.entity.enums.StoreMessageTypeEnum;
import cn.lili.modules.wechat.entity.enums.WechatMessageItemEnums;
import cn.lili.modules.wechat.mapper.StoreWechatMessageMapper;
import cn.lili.modules.wechat.service.StoreWechatMessageService;
import cn.lili.modules.wechat.util.WechatAccessTokenUtil;
import cn.lili.modules.wechat.util.WechatMessageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商家微信消息模板 业务实现
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class StoreWechatMessageServiceImpl extends ServiceImpl<StoreWechatMessageMapper, StoreWechatMessage> implements StoreWechatMessageService {

    @Autowired
    private WechatAccessTokenUtil wechatAccessTokenUtil;

    /**
     * 微信模板消息相关URL
     */
    private static final String ADD_TPL = "https://api.weixin.qq.com/cgi-bin/template/api_add_template?access_token=";
    private static final String SET_INDUSTRY = "https://api.weixin.qq.com/cgi-bin/template/api_set_industry?access_token=";
    private static final String ALL_MSG_TPL = "https://api.weixin.qq.com/cgi-bin/template/get_all_private_template?access_token=";

    @Override
    public void init() {
        try {
            // 清空现有模板
            this.baseMapper.deleteAll();

            // 获取token
            String accessToken = wechatAccessTokenUtil.cgiAccessToken(ClientTypeEnum.H5);

            // 设置行业
            Map<String, Object> setIndustryParams = new HashMap<>(16);
            // 互联网/电子商务
            setIndustryParams.put("industry_id1", 1);
            // 通信与运营商
            setIndustryParams.put("industry_id2", 5);
            String context = HttpUtils.doPostWithJson(SET_INDUSTRY + accessToken, setIndustryParams);
            log.info("设置模版请求{},设置行业响应：{}", setIndustryParams, context);

            // 初始化商家消息模板数据
            List<StoreWechatMessageData> tmpList = initStoreMessageData();
            tmpList.forEach(tplData -> {
                StoreWechatMessage storeWechatMessage = new StoreWechatMessage();
                Map<String, Object> params = new HashMap<>(1);
                params.put("template_id_short", tplData.getMsgId());
                String message = HttpUtils.doPostWithJson(ADD_TPL + accessToken, params);
                log.info("添加商家模版请求:{},添加模版响应：{}", params, message);

                JSONObject tplContent = new JSONObject(message);
                WechatMessageUtil.wechatHandler(tplContent);

                // 如果包含模版id则进行操作，否则抛出异常
                if (tplContent.containsKey("template_id")) {
                    storeWechatMessage.setCode(tplContent.getStr("template_id"));
                } else {
                    throw new ServiceException(ResultCode.WECHAT_MP_MESSAGE_TMPL_ERROR);
                }

                storeWechatMessage.setName(tplData.getName());
                storeWechatMessage.setFirst(tplData.getFirst());
                storeWechatMessage.setRemark(tplData.getRemark());
                storeWechatMessage.setKeywords(tplData.getKeyWord());
                storeWechatMessage.setEnable(true);
                storeWechatMessage.setOrderStatus(tplData.getOrderStatus().name());
                storeWechatMessage.setMessageType(tplData.getMessageType().name());
                this.save(storeWechatMessage);
            });
        } catch (Exception e) {
            log.error("初始化商家微信消息异常", e);
        }
    }

    @Override
    public StoreWechatMessage getByOrderStatusAndType(String orderStatus, String messageType) {
        LambdaQueryWrapper<StoreWechatMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreWechatMessage::getOrderStatus, orderStatus)
                   .eq(StoreWechatMessage::getMessageType, messageType)
                   .eq(StoreWechatMessage::getEnable, true);
        return this.getOne(queryWrapper);
    }

    /**
     * 初始化商家消息模板数据
     *
     * @return 模板数据列表
     */
    private List<StoreWechatMessageData> initStoreMessageData() {
        List<StoreWechatMessageData> msg = new ArrayList<>();
        
        // 订单付款通知
        msg.add(new StoreWechatMessageData(
                "新订单付款通知",
                "您有新的订单付款",
                "请及时处理订单，如有问题请联系客服",
                "OPENTM207498902",
                WechatMessageItemEnums.ORDER_SN.name() + "," + WechatMessageItemEnums.MEMBER_NAME.name() + "," +
                        WechatMessageItemEnums.PRICE.name() + "," + WechatMessageItemEnums.GOODS_INFO.name(),
                OrderStatusEnum.PAID,
                StoreMessageTypeEnum.ORDER_PAID));

        return msg;
    }

    /**
     * 商家微信消息模板数据类
     */
    private static class StoreWechatMessageData {
        private String name;
        private String first;
        private String remark;
        private String msgId;
        private String keyWord;
        private OrderStatusEnum orderStatus;
        private StoreMessageTypeEnum messageType;

        public StoreWechatMessageData(String name, String first, String remark, String msgId, 
                                    String keyWord, OrderStatusEnum orderStatus, StoreMessageTypeEnum messageType) {
            this.name = name;
            this.first = first;
            this.remark = remark;
            this.msgId = msgId;
            this.keyWord = keyWord;
            this.orderStatus = orderStatus;
            this.messageType = messageType;
        }

        // Getters
        public String getName() { return name; }
        public String getFirst() { return first; }
        public String getRemark() { return remark; }
        public String getMsgId() { return msgId; }
        public String getKeyWord() { return keyWord; }
        public OrderStatusEnum getOrderStatus() { return orderStatus; }
        public StoreMessageTypeEnum getMessageType() { return messageType; }
    }
}
