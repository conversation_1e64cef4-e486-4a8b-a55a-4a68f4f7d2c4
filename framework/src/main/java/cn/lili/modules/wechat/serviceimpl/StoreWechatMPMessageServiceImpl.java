package cn.lili.modules.wechat.serviceimpl;

import cn.hutool.json.JSONObject;
import cn.lili.common.enums.ClientTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.HttpUtils;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.wechat.entity.dos.StoreWechatMPMessage;
import cn.lili.modules.wechat.entity.enums.StoreMessageTypeEnum;
import cn.lili.modules.wechat.entity.enums.WechatMessageItemEnums;
import cn.lili.modules.wechat.mapper.StoreWechatMPMessageMapper;
import cn.lili.modules.wechat.service.StoreWechatMPMessageService;
import cn.lili.modules.wechat.util.WechatAccessTokenUtil;
import cn.lili.modules.wechat.util.WechatMessageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商家微信小程序消息模板 业务实现
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class StoreWechatMPMessageServiceImpl extends ServiceImpl<StoreWechatMPMessageMapper, StoreWechatMPMessage> implements StoreWechatMPMessageService {

    @Autowired
    private WechatAccessTokenUtil wechatAccessTokenUtil;

    /**
     * 微信小程序订阅消息相关URL
     */
    private static final String ADD_TPL = "https://api.weixin.qq.com/wxaapi/newtmpl/addtemplate?access_token=";

    @Override
    public void init() {
        try {
            // 清空现有模板
            this.baseMapper.deleteAll();

            // 获取token
            String accessToken = wechatAccessTokenUtil.cgiAccessToken(ClientTypeEnum.WECHAT_MP);

            // 初始化商家小程序消息模板数据
            List<StoreWechatMPMessageData> tmpList = initStoreMPMessageData();
            tmpList.forEach(tplData -> {
                StoreWechatMPMessage storeWechatMPMessage = new StoreWechatMPMessage();

                Map<String, Object> params = new HashMap<>(4);
                params.put("tid", tplData.getTid());
                params.put("kidList", tplData.getKidList());
                params.put("sceneDesc", tplData.getSceneDesc());
                
                String content = HttpUtils.doPostWithJson(ADD_TPL + accessToken, params);
                log.info("添加商家小程序模版参数:{},添加模版响应:{}", params, content);
                JSONObject tplContent = new JSONObject(content);
                WechatMessageUtil.wechatHandler(tplContent);

                // 如果包含模版id则进行操作，否则抛出异常
                if (tplContent.containsKey("priTmplId")) {
                    storeWechatMPMessage.setCode(tplContent.getStr("priTmplId"));
                } else {
                    throw new ServiceException(ResultCode.WECHAT_MP_MESSAGE_TMPL_ERROR);
                }

                storeWechatMPMessage.setName(tplData.getName());
                storeWechatMPMessage.setKeywords(tplData.getKeywords());
                storeWechatMPMessage.setKeywordsText(tplData.getKeywordsText());
                storeWechatMPMessage.setEnable(true);
                storeWechatMPMessage.setOrderStatus(tplData.getOrderStatus().name());
                storeWechatMPMessage.setMessageType(tplData.getMessageType().name());
                storeWechatMPMessage.setSceneDesc(tplData.getSceneDesc());
                this.save(storeWechatMPMessage);
            });
        } catch (Exception e) {
            log.error("初始化商家微信小程序消息异常", e);
        }
    }

    @Override
    public StoreWechatMPMessage getByOrderStatusAndType(String orderStatus, String messageType) {
        LambdaQueryWrapper<StoreWechatMPMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreWechatMPMessage::getOrderStatus, orderStatus)
                   .eq(StoreWechatMPMessage::getMessageType, messageType)
                   .eq(StoreWechatMPMessage::getEnable, true);
        return this.getOne(queryWrapper);
    }

    /**
     * 初始化商家小程序消息模板数据
     *
     * @return 模板数据列表
     */
    private List<StoreWechatMPMessageData> initStoreMPMessageData() {
        List<StoreWechatMPMessageData> msg = new ArrayList<>();
        
        // 订单付款通知
        msg.add(new StoreWechatMPMessageData(
                "新订单付款通知",
                "[\"" + WechatMessageItemEnums.ORDER_SN.name() + "\",\"" + WechatMessageItemEnums.MEMBER_NAME.name() + "\",\"" +
                        WechatMessageItemEnums.PRICE.name() + "\",\"" + WechatMessageItemEnums.GOODS_INFO.name() + "\"]",
                "[\"订单号\",\"买家\",\"订单金额\",\"商品信息\"]",
                "AT0002",
                "[1,2,3,4]",
                OrderStatusEnum.PAID,
                StoreMessageTypeEnum.ORDER_PAID,
                "商家收到新订单付款时通知"));

        return msg;
    }

    /**
     * 商家微信小程序消息模板数据类
     */
    private static class StoreWechatMPMessageData {
        private String name;
        private String keywords;
        private String keywordsText;
        private String tid;
        private String kidList;
        private OrderStatusEnum orderStatus;
        private StoreMessageTypeEnum messageType;
        private String sceneDesc;

        public StoreWechatMPMessageData(String name, String keywords, String keywordsText, String tid, 
                                      String kidList, OrderStatusEnum orderStatus, StoreMessageTypeEnum messageType, String sceneDesc) {
            this.name = name;
            this.keywords = keywords;
            this.keywordsText = keywordsText;
            this.tid = tid;
            this.kidList = kidList;
            this.orderStatus = orderStatus;
            this.messageType = messageType;
            this.sceneDesc = sceneDesc;
        }

        // Getters
        public String getName() { return name; }
        public String getKeywords() { return keywords; }
        public String getKeywordsText() { return keywordsText; }
        public String getTid() { return tid; }
        public String getKidList() { return kidList; }
        public OrderStatusEnum getOrderStatus() { return orderStatus; }
        public StoreMessageTypeEnum getMessageType() { return messageType; }
        public String getSceneDesc() { return sceneDesc; }
    }
}
