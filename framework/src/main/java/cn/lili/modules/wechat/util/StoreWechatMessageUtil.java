package cn.lili.modules.wechat.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ClientTypeEnum;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.DateUtil;
import cn.lili.common.utils.StringUtils;
import cn.lili.modules.connect.entity.enums.SourceEnum;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.store.entity.dos.StoreConnect;
import cn.lili.modules.store.service.StoreConnectService;
import cn.lili.modules.wechat.entity.dos.StoreWechatMPMessage;
import cn.lili.modules.wechat.entity.dos.StoreWechatMessage;
import cn.lili.modules.wechat.entity.enums.StoreMessageTypeEnum;
import cn.lili.modules.wechat.entity.enums.WechatMessageItemEnums;
import cn.lili.modules.wechat.service.StoreWechatMPMessageService;
import cn.lili.modules.wechat.service.StoreWechatMessageService;
import cn.lili.modules.system.service.StoreWechatNotificationSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 商家微信消息发送工具类
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Component
public class StoreWechatMessageUtil {

    @Autowired
    private StoreConnectService storeConnectService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderItemService orderItemService;
    @Autowired
    private WechatAccessTokenUtil wechatAccessTokenUtil;
    @Autowired
    private StoreWechatMessageService storeWechatMessageService;
    @Autowired
    private StoreWechatMPMessageService storeWechatMPMessageService;
    @Autowired
    private StoreWechatNotificationSettingService storeWechatNotificationSettingService;

    /**
     * 发送商家微信通知
     *
     * @param orderSn     订单编号
     * @param messageType 消息类型
     */
    public void sendStoreWechatMessage(String orderSn, StoreMessageTypeEnum messageType) {
        // 检查是否启用该类型的通知
        if (!storeWechatNotificationSettingService.isNotificationEnabled(messageType.name())) {
            log.info("商家微信通知已禁用，消息类型：{}", messageType.name());
            return;
        }

        // 检查是否在允许的时间范围内
        if (!storeWechatNotificationSettingService.isInNotificationTimeRange()) {
            log.info("当前时间不在商家微信通知允许的时间范围内");
            return;
        }

        try {
            this.sendStoreWechatPublicMessage(orderSn, messageType);
        } catch (Exception e) {
            log.error("发送商家微信公众号消息异常：", e);
        }
        try {
            this.sendStoreWechatMpMessage(orderSn, messageType);
        } catch (Exception e) {
            log.error("发送商家小程序消息订阅异常：", e);
        }
    }

    /**
     * 发送商家微信公众号消息
     *
     * @param orderSn     订单编号
     * @param messageType 消息类型
     */
    public void sendStoreWechatPublicMessage(String orderSn, StoreMessageTypeEnum messageType) {
        Order order = orderService.getBySn(orderSn);
        if (order == null) {
            throw new ServiceException("订单" + orderSn + "不存在，发送商家微信公众号消息错误");
        }

        // 获取商家微信绑定信息
        StoreConnect storeConnect = storeConnectService.getByStoreIdAndType(
                order.getStoreId(), SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        
        if (storeConnect == null || !storeConnect.getEnableNotification()) {
            log.info("商家{}未绑定微信公众号或未启用通知", order.getStoreName());
            return;
        }

        // 获取消息模板
        StoreWechatMessage wechatMessage = storeWechatMessageService.getByOrderStatusAndType(
                order.getOrderStatus(), messageType.name());

        if (wechatMessage == null) {
            log.error("未配置商家微信公众号消息模板，订单状态：{}，消息类型：{}", order.getOrderStatus(), messageType.name());
            return;
        }

        log.info("发送商家微信消息：{}-{}", order.getStoreName(), orderSn);
        
        // 获取token
        String token = wechatAccessTokenUtil.cgiAccessToken(ClientTypeEnum.H5);

        // 发送url
        String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + token;

        Map<String, Object> map = new HashMap<>(4);
        // 商家微信用户id
        map.put("touser", storeConnect.getUnionId());
        // 模版id
        map.put("template_id", wechatMessage.getCode());
        // 模版中所需数据
        map.put("data", createStoreData(order, wechatMessage));

        log.info("商家微信消息参数内容：" + JSONUtil.toJsonStr(map));
        String content = HttpUtil.post(url, JSONUtil.toJsonStr(map));
        JSONObject json = new JSONObject(content);
        log.info("商家微信消息发送结果：" + content);
        String errorMessage = json.getStr("errmsg");
        String errcode = json.getStr("errcode");
        // 发送失败
        if (!"0".equals(errcode)) {
            log.error("商家微信消息发送失败：" + errorMessage);
            log.error("商家微信消息发送请求token：" + token);
            log.error("商家微信消息发送请求：" + map.get("data"));
        }
    }

    /**
     * 发送商家微信小程序消息
     *
     * @param orderSn     订单编号
     * @param messageType 消息类型
     */
    public void sendStoreWechatMpMessage(String orderSn, StoreMessageTypeEnum messageType) {
        log.info("发送商家小程序消息订阅");
        Order order = orderService.getBySn(orderSn);
        if (order == null) {
            throw new ServiceException("订单" + orderSn + "不存在，发送商家订阅消息错误");
        }

        // 获取商家微信小程序绑定信息
        StoreConnect storeConnect = storeConnectService.getByStoreIdAndType(
                order.getStoreId(), SourceEnum.WECHAT_MP_OPEN_ID.name());
        
        if (storeConnect == null || !storeConnect.getEnableNotification()) {
            log.info("商家{}未绑定微信小程序或未启用通知", order.getStoreName());
            return;
        }

        // 获取消息模板
        StoreWechatMPMessage wechatMPMessage = storeWechatMPMessageService.getByOrderStatusAndType(
                order.getOrderStatus(), messageType.name());
        if (wechatMPMessage == null) {
            log.info("未配置商家微信小程序消息订阅模板，订单状态：{}，消息类型：{}", order.getOrderStatus(), messageType.name());
            return;
        }

        log.info("发送商家微信消息订阅消息：{}-{}", order.getStoreName(), orderSn);
        // 获取token
        String token = wechatAccessTokenUtil.cgiAccessToken(ClientTypeEnum.WECHAT_MP);

        // 发送url
        String url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + token;

        Map<String, Object> map = new HashMap<>(4);
        // 商家微信用户id
        map.put("touser", storeConnect.getUnionId());
        // 模版id
        map.put("template_id", wechatMPMessage.getCode());
        // 模版中所需数据
        map.put("data", createStoreData(order, wechatMPMessage));
        // 跳转页面（商家端订单详情页）
        map.put("page", "pages/order/orderDetail?sn=" + order.getSn());
        
        log.info("商家小程序消息参数内容：" + JSONUtil.toJsonStr(map));
        String content = null;
        try {
            content = HttpUtil.post(url, JSONUtil.toJsonStr(map));
        } catch (Exception e) {
            log.error("商家微信小程序消息发送错误", e);
        }
        JSONObject json = new JSONObject(content);
        log.info("商家微信小程序消息发送结果：" + content);
        String errorMessage = json.getStr("errmsg");
        String errcode = json.getStr("errcode");
        // 发送失败
        if (!"0".equals(errcode)) {
            log.error("商家小程序消息发送失败：" + errorMessage);
            log.error("商家小程序消息发送请求token：" + token);
            log.error("商家小程序消息发送请求：" + map.get("data"));
        }
    }

    /**
     * 构造商家微信公众号消息数据
     *
     * @param order         订单信息
     * @param wechatMessage 微信消息模板
     * @return 消息数据
     */
    private Map<String, Map<String, String>> createStoreData(Order order, StoreWechatMessage wechatMessage) {
        WechatMessageData wechatMessageData = new WechatMessageData();
        wechatMessageData.setFirst(wechatMessage.getFirst());
        wechatMessageData.setRemark(wechatMessage.getRemark());
        String[] paramArray = wechatMessage.getKeywords().split(",");
        LinkedList<String> params = new LinkedList<>();

        for (String param : paramArray) {
            WechatMessageItemEnums wechatMessageItemEnums = WechatMessageItemEnums.valueOf(param);
            // 初始化参数内容
            String val = getStoreParams(wechatMessageItemEnums, order);
            params.add(val);
        }
        wechatMessageData.setMessageData(params);
        return wechatMessageData.createData();
    }

    /**
     * 构造商家微信小程序消息数据
     *
     * @param order           订单信息
     * @param wechatMPMessage 微信小程序消息模板
     * @return 消息数据
     */
    private Map<String, Map<String, String>> createStoreData(Order order, StoreWechatMPMessage wechatMPMessage) {
        WechatMessageData wechatMessageData = new WechatMessageData();
        List<String> paramArray = JSONUtil.toList(wechatMPMessage.getKeywords(), String.class);
        List<String> texts = JSONUtil.toList(wechatMPMessage.getKeywordsText(), String.class);
        Map<String, String> params = new LinkedHashMap<>();
        for (int i = 0; i < paramArray.size(); i++) {
            WechatMessageItemEnums wechatMessageItemEnums = WechatMessageItemEnums.valueOf(paramArray.get(i));
            // 初始化参数内容
            String val = getStoreParams(wechatMessageItemEnums, order);
            val = StringUtils.subStringLength(val, 20);
            params.put(texts.get(i), val);
        }
        wechatMessageData.setMpMessageData(params);
        return wechatMessageData.createMPData();
    }

    /**
     * 获取商家消息具体参数
     *
     * @param itemEnums 参数枚举
     * @param order     订单信息
     * @return 参数值
     */
    private String getStoreParams(WechatMessageItemEnums itemEnums, Order order) {
        switch (itemEnums) {
            case PRICE:
                return order.getPriceDetailDTO().getFlowPrice().toString();
            case ORDER_SN:
                return order.getSn();
            case SHOP_NAME:
                return order.getStoreName();
            case GOODS_INFO:
                List<OrderItem> orderItems = orderItemService.getByOrderSn(order.getSn());
                StringBuffer stringBuffer = new StringBuffer();
                orderItems.forEach(orderItem -> {
                    stringBuffer.append(orderItem.getGoodsName() + "*" + orderItem.getNum() + "  ");
                });
                return stringBuffer.toString();
            case MEMBER_NAME:
                return order.getMemberName();
            case LOGISTICS_NO:
                return order.getLogisticsNo();
            case LOGISTICS_NAME:
                return order.getLogisticsName();
            case LOGISTICS_TIME:
                return DateUtil.toString(order.getLogisticsTime(), DateUtil.STANDARD_FORMAT);
            default:
                return "";
        }
    }
}
