package cn.lili.modules.wechat.mapper;

import cn.lili.modules.wechat.entity.dos.StoreWechatMessage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;

/**
 * 商家微信消息模板 数据处理层
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface StoreWechatMessageMapper extends BaseMapper<StoreWechatMessage> {

    /**
     * 删除所有商家微信消息模板
     */
    @Delete("delete from li_store_wechat_message")
    void deleteAll();
}
