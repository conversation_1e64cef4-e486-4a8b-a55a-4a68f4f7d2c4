package cn.lili.modules.wechat.entity.enums;

/**
 * 商家微信消息类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public enum StoreMessageTypeEnum {

    /**
     * 订单付款通知
     */
    ORDER_PAID("订单付款通知"),

    /**
     * 订单发货通知
     */
    ORDER_DELIVERED("订单发货通知"),

    /**
     * 订单完成通知
     */
    ORDER_COMPLETED("订单完成通知"),

    /**
     * 订单取消通知
     */
    ORDER_CANCELLED("订单取消通知");

    private final String description;

    StoreMessageTypeEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String description() {
        return this.description;
    }
}
