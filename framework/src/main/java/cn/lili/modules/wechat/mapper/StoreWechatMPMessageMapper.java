package cn.lili.modules.wechat.mapper;

import cn.lili.modules.wechat.entity.dos.StoreWechatMPMessage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;

/**
 * 商家微信小程序消息模板 数据处理层
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface StoreWechatMPMessageMapper extends BaseMapper<StoreWechatMPMessage> {

    /**
     * 删除所有商家微信小程序消息模板
     */
    @Delete("delete from li_store_wechat_mp_message")
    void deleteAll();
}
