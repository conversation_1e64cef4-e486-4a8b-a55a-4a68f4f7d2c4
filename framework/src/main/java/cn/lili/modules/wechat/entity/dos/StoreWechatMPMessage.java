package cn.lili.modules.wechat.entity.dos;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商家微信小程序消息模板
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@TableName("li_store_wechat_mp_message")
@ApiModel(value = "商家微信小程序消息模板")
@NoArgsConstructor
public class StoreWechatMPMessage extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "模板编码")
    private String code;

    @ApiModelProperty(value = "模板关键字")
    private String keywords;

    @ApiModelProperty(value = "模板关键字文本")
    private String keywordsText;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "消息类型：ORDER_PAID-订单付款，ORDER_DELIVERED-订单发货")
    private String messageType;

    @ApiModelProperty(value = "场景描述")
    private String sceneDesc;

    public StoreWechatMPMessage(String name, String keywords, String keywordsText, String orderStatus, String messageType, String sceneDesc) {
        this.name = name;
        this.keywords = keywords;
        this.keywordsText = keywordsText;
        this.orderStatus = orderStatus;
        this.messageType = messageType;
        this.sceneDesc = sceneDesc;
        this.enable = true;
    }
}
