package cn.lili.modules.wechat.service;

import cn.lili.modules.wechat.entity.dos.StoreWechatMPMessage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商家微信小程序消息模板 业务层
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface StoreWechatMPMessageService extends IService<StoreWechatMPMessage> {

    /**
     * 初始化商家微信小程序消息模板
     */
    void init();

    /**
     * 根据订单状态和消息类型获取消息模板
     *
     * @param orderStatus 订单状态
     * @param messageType 消息类型
     * @return 消息模板
     */
    StoreWechatMPMessage getByOrderStatusAndType(String orderStatus, String messageType);
}
