package cn.lili.modules.connect.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.cache.Cache;
import cn.lili.common.enums.ClientTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.properties.RocketmqCustomProperties;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.token.Token;
import cn.lili.common.utils.HttpUtils;
import cn.lili.common.utils.UuidUtils;
import cn.lili.modules.connect.entity.Connect;
import cn.lili.modules.connect.entity.dto.AuthToken;
import cn.lili.modules.connect.entity.dto.ConnectAuthUser;
import cn.lili.modules.connect.entity.dto.MemberConnectLoginMessage;
import cn.lili.modules.connect.entity.dto.WechatMPLoginParams;
import cn.lili.modules.connect.entity.enums.ConnectEnum;
import cn.lili.modules.connect.entity.enums.SourceEnum;
import cn.lili.modules.connect.mapper.ConnectMapper;
import cn.lili.modules.connect.service.ConnectService;
import cn.lili.modules.member.entity.dos.Member;
import cn.lili.modules.member.service.MemberService;
import cn.lili.modules.member.token.MemberTokenGenerate;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.connect.WechatConnectSetting;
import cn.lili.modules.system.entity.dto.connect.dto.WechatConnectSettingItem;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.system.service.SettingService;
import cn.lili.modules.wechat.util.WechatMpCodeUtil;
import cn.lili.rocketmq.RocketmqSendCallbackBuilder;
import cn.lili.rocketmq.tags.MemberTagsEnum;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.naming.NoPermissionException;

import java.nio.charset.StandardCharsets;
import java.security.AlgorithmParameters;
import java.security.Security;
import java.util.*;

/**
 * 联合登陆接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ConnectServiceImpl extends ServiceImpl<ConnectMapper, Connect> implements ConnectService {

    static final boolean AUTO_REGION = true;

    @Autowired
    private SettingService settingService;
    @Autowired
    private MemberService memberService;
    @Autowired
    private MemberTokenGenerate memberTokenGenerate;
    @Autowired
    private Cache cache;
    /**
     * RocketMQ
     */
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    /**
     * RocketMQ配置
     */
    @Autowired
    private RocketmqCustomProperties rocketmqCustomProperties;
    @Autowired
    public WechatMpCodeUtil wechatMpCodeUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Token unionLoginCallback(ConnectAuthUser authUser, String uuid) {
        return this.unionLoginCallback(authUser, false);
    }

    @Override
    public void bind(String unionId, String type) {
        AuthUser authUser = Objects.requireNonNull(UserContext.getCurrentUser());
        Connect connect = new Connect(authUser.getId(), unionId, type);
        this.save(connect);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbind(String type) {

        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(Connect::getUserId, UserContext.getCurrentUser().getId());
        queryWrapper.eq(Connect::getUnionType, type);

        this.remove(queryWrapper);
    }

    @Override
    public List<String> bindList() {
        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Connect::getUserId, UserContext.getCurrentUser().getId());
        List<Connect> connects = this.list(queryWrapper);
        List<String> keys = new ArrayList<>();
        connects.forEach(item -> keys.add(item.getUnionType()));
        return keys;
    }

    @Override
    @Transactional
    public Token miniProgramAutoLogin(WechatMPLoginParams params) {

        Map<String, String> map = new HashMap<>(3);
        // 得宝微信小程序联合登陆信息
        JSONObject json = this.getConnect(params.getCode());
        // String accessToken = wechatMpCodeUtil.obtainAccessToken();
        String phoneNum = params.getPhoneNumber();
        // 存储session key 后续登录用得到
        String sessionKey = json.getStr("session_key");
        String unionId = json.getStr("unionid");
        String openId = json.getStr("openid");
        map.put("sessionKey", sessionKey);
        map.put("unionId", unionId);
        map.put("openId", openId);

        // 微信联合登陆参数
        return phoneMpBindAndLogin(map.get("sessionKey"), params, map.get("openId"), map.get("unionId"));
    }

    /**
     * 通过微信返回等code 获取openid 等信息
     *
     * @param code 微信code
     * @return 微信返回的信息
     */
    public JSONObject getConnect(String code) {
        WechatConnectSettingItem setting = getWechatMPSetting();
        String url = "https://api.weixin.qq.com/sns/jscode2session?" + "appid=" + setting.getAppId() + "&" + "secret="
                + setting.getAppSecret() + "&" + "js_code=" + code + "&" + "grant_type=authorization_code";
        String content = HttpUtils.doGet(url, "UTF-8", 100, 1000);
        log.error(content);
        return JSONUtil.parseObj(content);
    }

    /**
     * 手机号 绑定 且 自动登录
     *
     * @param sessionKey 微信sessionKey
     * @param params     微信小程序自动登录参数
     * @param openId     微信openid
     * @param unionId    微信unionid
     * @return token
     */
    @Transactional(rollbackFor = Exception.class)
    public Token phoneMpBindAndLogin(String sessionKey, WechatMPLoginParams params, String openId, String unionId) {
        try {
            // String encryptedData = params.getEncryptedData();
            // String iv = params.getIv();
            // JSONObject userInfo = this.getUserInfo(encryptedData, sessionKey, iv);
            String phone = params.getPhoneNumber();
            log.info("==============联合登录,openId:{},unionId:{}", openId, unionId);
            // log.info("联合登陆返回：{}", userInfo.toString());

            ConnectAuthUser connectAuthUser = new ConnectAuthUser();
            connectAuthUser.setUuid(openId);
            connectAuthUser.setNickname(params.getNickName());
            connectAuthUser.setAvatar(params.getImage());

            if (phone != null) {
                connectAuthUser.setUsername(phone);
                connectAuthUser.setPhone(phone);
            } else {
                connectAuthUser.setUsername(UuidUtils.getUUID());
                // 确保phone不为null
                connectAuthUser.setPhone("");
            }
            connectAuthUser.setSource(ConnectEnum.WECHAT);
            connectAuthUser.setType(ClientTypeEnum.WECHAT_MP);

            AuthToken authToken = new AuthToken();
            authToken.setOpenId(openId);
            authToken.setUnionId(unionId);
            connectAuthUser.setToken(authToken);
            return this.unionLoginCallback(connectAuthUser, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Connect queryConnect(String userId, String unionId, String unionType) {

        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(CharSequenceUtil.isNotEmpty(userId), Connect::getUserId,
                userId)
                .eq(CharSequenceUtil.isNotEmpty(unionType), Connect::getUnionType,
                unionType)
                .eq(CharSequenceUtil.isNotEmpty(unionId), Connect::getUnionId,
                unionId);
        return this.getOne(queryWrapper, false);
    }

    @Override
    public void deleteByMemberId(String userId) {
        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Connect::getUserId, userId);
        this.remove(queryWrapper);
    }

    /**
     * 成功登录，则检测cookie中的信息，进行会员绑定
     *
     * @param userId  用户ID
     * @param unionId 第三方用户ID
     * @param type    类型
     */
    @Override
    public void loginBindUser(String userId, String unionId, String type) {
        Connect connect = this.queryConnect(userId, unionId, type);
        // 如果未绑定则直接绑定
        if (connect == null) {
            connect = new Connect(userId, unionId, type);
            this.save(connect);
            // 如果已绑定不是当前用户信息则删除绑定信息，重新绑定
        } else if (!connect.getUserId().equals(userId)) {
            this.removeById(connect.getId());
            this.loginBindUser(userId, unionId, type);
        }
    }

    /**
     * 第三方联合登陆 1.判断是否使用开放平台 1.1如果使用开放平台则使用UnionId进行登录 1.2如果不适用开放平台则使用OpenId进行登录
     * <p>
     * 2.用户登录后判断绑定OpenId
     *
     * @param authUser 第三方登录封装类
     * @param longTerm 是否长时间有效
     * @return token
     * @throws NoPermissionException 不允许操作
     */
    @Transactional
    private Token unionLoginCallback(ConnectAuthUser authUser, boolean longTerm) {
        //////@TODO 要求麓点通传递是，电话号码不要带m！！！！！！！！！！！！！！！！！！！
        String phone = authUser.getPhone();
        authUser.setPhone(phone.replaceFirst("m", ""));
        String openId = authUser.getToken().getOpenId();

        // 如果从第三方登录过来的，根据code获取其在得宝商城的openId
        String accessCode = authUser.getToken().getAccessCode();
        if (CharSequenceUtil.isNotEmpty(accessCode)) {
            log.info("通过其他小程序联合登录跳转过来，带参传过来的openId："+openId);
            log.info("通过其他小程序联合登录跳转过来，wx.login请求的code："+accessCode);
            // 得宝微信小程序联合登陆信息
            JSONObject json = this.getConnect(accessCode);
            //如果第三方登录过来的，openId为根据code获取的得宝商城内openId
            openId = json.getStr("openid");
            if (CharSequenceUtil.isNotEmpty(openId)) {
                authUser.getToken().setOpenId(openId);
                log.info("通过其他小程序联合登录跳转过来，根据code获得其在得宝商城的openId：{}", openId);
            } else {
                log.warn("未能从第三方获取有效的openId");
            }
       }

        Member member = memberService.findByMobile(authUser.getPhone());
        if (member == null) {
            member = memberService.autoRegister(authUser);
            // bindUser(member.getId(), authUser);
        } else {
            Connect connect = this.queryConnect(member.getId(), authUser.getToken().getUnionId(), authUser.getSource().name());
            log.info("联合登录，unoinid {},openid: {}",authUser.getToken().getUnionId(),authUser.getToken().getOpenId());
            if (connect != null && !member.getId().equals(connect.getUserId())) {
                // 如果已绑定不是当前用户信息则删除绑定信息，重新绑定
                this.removeById(connect.getId());
            }
            // bindUser(member.getId(), authUser);
        }

        // 发送用户第三方登录消息
        log.info("发送给consumer的authUser，传的openId："+authUser.getToken().getOpenId());
        sendMemberConnectLoginMessage(member, authUser);

        return memberTokenGenerate.createToken(member, longTerm);

    }

    public void bindUser(String memberId, ConnectAuthUser authUser) {
        // 优先绑定 UnionId
        Connect existingConnect = this.queryConnect(memberId, authUser.getToken().getUnionId(), authUser.getSource().name());
        if (existingConnect == null) {
            this.loginBindUser(memberId, authUser.getToken().getUnionId(), authUser.getSource().name());
        }
    
        // 检查 OpenId 绑定
        existingConnect = null;
        existingConnect = this.queryConnect(memberId, authUser.getToken().getOpenId(), SourceEnum.getSourceEnum(authUser.getSource(), authUser.getType()).name());
        if (existingConnect == null) {
            this.loginBindUser(memberId, authUser.getToken().getOpenId(),
                    SourceEnum.getSourceEnum(authUser.getSource(), authUser.getType()).name());
        }
    }
    

    private void sendMemberConnectLoginMessage(Member member, ConnectAuthUser authUser) {
        MemberConnectLoginMessage message = new MemberConnectLoginMessage();
        message.setMember(member);
        message.setConnectAuthUser(authUser);
        String destination = rocketmqCustomProperties.getMemberTopic() + ":"
                + MemberTagsEnum.MEMBER_CONNECT_LOGIN.name();
        rocketMQTemplate.asyncSend(destination, JSONUtil.toJsonStr(message),
                RocketmqSendCallbackBuilder.commonCallback());
    }

    /**
     * 获取微信小程序配置
     *
     * @return 微信小程序配置
     */
    private WechatConnectSettingItem getWechatMPSetting() {
        Setting setting = settingService.get(SettingEnum.WECHAT_CONNECT.name());

        WechatConnectSetting wechatConnectSetting = JSONUtil.toBean(setting.getSettingValue(),
                WechatConnectSetting.class);

        if (wechatConnectSetting == null) {
            throw new ServiceException(ResultCode.WECHAT_CONNECT_NOT_EXIST);
        }
        // 寻找对应对微信小程序登录配置
        for (WechatConnectSettingItem wechatConnectSettingItem : wechatConnectSetting.getWechatConnectSettingItems()) {
            if (wechatConnectSettingItem.getClientType().equals(ClientTypeEnum.WECHAT_MP.name())) {
                return wechatConnectSettingItem;
            }
        }

        throw new ServiceException(ResultCode.WECHAT_CONNECT_NOT_EXIST);
    }

    /**
     * 解密，获取微信信息
     *
     * @param encryptedData 加密信息
     * @param sessionKey    微信sessionKey
     * @param iv            微信揭秘参数
     * @return 用户信息
     */
    public JSONObject getUserInfo(String encryptedData, String sessionKey, String iv) {

        log.info("encryptedData:{},sessionKey:{},iv:{}", encryptedData, sessionKey, iv);
        // 被加密的数据
        byte[] dataByte = Base64.getDecoder().decode(encryptedData);
        // 加密秘钥
        byte[] keyByte = Base64.getDecoder().decode(sessionKey);
        // 偏移量
        byte[] ivByte = Base64.getDecoder().decode(iv);
        try {
            // 如果密钥不足16位，那么就补足. 这个if 中的内容很重要
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            // 初始化
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, StandardCharsets.UTF_8);
                return JSONUtil.parseObj(result);
            }
        } catch (Exception e) {
            log.error("解密，获取微信信息错误", e);
        }
        throw new ServiceException(ResultCode.USER_CONNECT_ERROR);
    }

    /**
     * 根据会员id查找UnionId
     *
     * @param userId 会员id
     */
    @Override
    public String getUnionIdByMemberId(String userId) {
        LambdaQueryWrapper<Connect> queryWrapper = new LambdaQueryWrapper<Connect>();
        queryWrapper.eq(Connect::getUserId, userId).eq(Connect::getUnionType, "WECHAT");
        // 查询绑定关系
        Connect connect = this.getOne(queryWrapper);

        if (connect == null) {
            log.error("该用户未绑定微信!");
            throw new ServiceException(ResultCode.USER_NOT_BIND_WECHAT_ERROR);
        }
        return connect.getUnionId();
    }
}