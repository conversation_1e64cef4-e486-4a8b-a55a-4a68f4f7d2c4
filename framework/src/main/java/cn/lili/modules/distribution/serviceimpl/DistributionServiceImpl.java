package cn.lili.modules.distribution.serviceimpl;

import cn.hutool.json.JSONUtil;
import cn.lili.cache.Cache;
import cn.lili.cache.CachePrefix;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.adapay.entity.dto.AdapayAccountInfo;
import cn.lili.modules.adapay.entity.enums.AdapayBankAccTypeEnum;
import cn.lili.modules.adapay.entity.enums.AdapayCertTypeEnum;
import cn.lili.modules.adapay.entity.enums.AdapayTradeStatusEnum;
import cn.lili.modules.adapay.service.AdapayACHService;
import cn.lili.modules.distribution.entity.dos.Distribution;
import cn.lili.modules.distribution.entity.dto.DistributionApplyDTO;
import cn.lili.modules.distribution.entity.dto.DistributionSearchParams;
import cn.lili.modules.distribution.entity.enums.DistributionStatusEnum;
import cn.lili.modules.distribution.mapper.DistributionMapper;
import cn.lili.modules.distribution.service.DistributionService;
import cn.lili.modules.member.entity.dos.Member;
import cn.lili.modules.member.service.MemberService;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.DistributionSetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.system.service.SettingService;
import cn.lili.mybatis.util.PageUtil;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 分销员接口实现
 *
 * <AUTHOR>
 * @since 2020-03-14 23:04:56
 */
@Service
@Slf4j
public class DistributionServiceImpl extends ServiceImpl<DistributionMapper, Distribution>
        implements DistributionService {

    /**
     * 会员
     */
    @Autowired
    private MemberService memberService;
    /**
     * 缓存
     */
    @Autowired
    private Cache cache;
    /**
     * 设置
     */
    @Autowired
    private SettingService settingService;
    /**
     * 分账服务
     */
    @Autowired
    private AdapayACHService adapayACHService;

    @Override
    public IPage<Distribution> distributionPage(DistributionSearchParams distributionSearchParams, PageVO page) {
        return this.page(PageUtil.initPage(page), distributionSearchParams.queryWrapper());
    }

    @Override
    public Distribution getDistribution() {

        return this.getOne(new LambdaQueryWrapper<Distribution>().eq(Distribution::getMemberId,
                UserContext.getCurrentUser().getId()));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Distribution applyDistribution(DistributionApplyDTO distributionApplyDTO) {

        // 检查分销开关
        checkDistributionSetting();

        // 判断用户是否申请过分销
        Distribution distribution = getDistribution();

        // 如果分销员非空并未审核则提示用户请等待，如果分销员为拒绝状态则重新提交申请
        if (Optional.ofNullable(distribution).isPresent()) {
            switch (DistributionStatusEnum.valueOf(distribution.getDistributionStatus())) {
            case REFUSE:
            case RETREAT:
                distribution.setDistributionStatus(DistributionStatusEnum.APPLY.name());
                BeanUtil.copyProperties(distributionApplyDTO, distribution);
                this.updateById(distribution);
                return distribution;
            default:
                throw new ServiceException(ResultCode.DISTRIBUTION_IS_APPLY);
            }
        } else {
            // 如果未申请分销员则新增进行申请
            // 获取当前登录用户
            Member member = memberService.getUserInfo();
            // 新建分销员
            distribution = new Distribution(member.getId(), member.getNickName(), distributionApplyDTO);
            // 添加分销员
            this.save(distribution);
        }
        return distribution;
    }

    @Override
    public boolean audit(String id, String status) {

        // 检查分销开关
        checkDistributionSetting();

        // 根据id获取分销员
        Distribution distribution = this.getById(id);
        if (distribution != null) {
            // 1.创建实名用户对象
            String memberId = distribution.getMemberId();

            // 在adapay中创建用户对象,首先判断是否已存在该用户
            Map<String, Object> adapayMember = adapayACHService.queryAdapayMember(memberId);
            boolean isMemberUpdated = updateOrCreateAdapayMember(adapayMember, memberId, distribution);

            if (isMemberUpdated) {
                // 将实名信息同步到用户表中
                Member member = memberService.getById(memberId);
                member.setRealName(distribution.getName());
                member.setCertId(distribution.getIdNumber());
                memberService.updateById(member);
            } else {
                log.error("会员：{}，创建分账系统用户对象失败", memberId);
                throw new ServiceException(ResultCode.ADAPAY_CRTEATE_MEMBER_ERROR);
            }

            // 2.创建结算账户对象
            AdapayAccountInfo accountInfo = new AdapayAccountInfo();
            accountInfo.setCardId(distribution.getSettlementBankAccountNum());
            accountInfo.setCardName(distribution.getSettlementBankAccountName());
            accountInfo.setCertId(distribution.getIdNumber());
            accountInfo.setCertType(AdapayCertTypeEnum.IDCARD.getCode());
            String telNo = memberService.getById(distribution.getMemberId()).getMobile();
            accountInfo.setTelNo(telNo);
            accountInfo.setBankAcctType(AdapayBankAccTypeEnum.PRIVATE.getCode());
            
            String settlementAccountId = distribution.getSettlementAccountId();
            Map<String, Object> settleAccount = adapayACHService.queryAdapaySettleAccount(memberId, settlementAccountId);

            if (settleAccount != null && settleAccount.get("status").equals(AdapayTradeStatusEnum.succeeded.name())) {
                // 结算账户无法更新，只能先删除，再重新创建
                adapayACHService.deleteAdapaySettleAccount(memberId, settlementAccountId);
                settleAccount = null;
            }
    
            settleAccount = adapayACHService.createAdapaySettleAccount(memberId, accountInfo);
            if (settleAccount != null && settleAccount.get("status").equals(AdapayTradeStatusEnum.succeeded.name())) {
                // 创建用户结算账户成功，更新到用户详细信息中
                distribution.setSettlementAccountId((String) settleAccount.get("id"));
                log.info("分账系统创建用户" + distribution.getName() + "的结算账户成功");
            } else {
                log.error("分账系统创建用户" + distribution.getName() + "的结算账户失败，请检查参数是否正确");
                throw new ServiceException(ResultCode.ADAPAY_CREATE_SETTLE_ACCOUNT_ERROR);
            }

            // 更新申请状态
            if (status.equals(DistributionStatusEnum.PASS.name())) {
                distribution.setDistributionStatus(DistributionStatusEnum.PASS.name());
            } else {
                distribution.setDistributionStatus(DistributionStatusEnum.REFUSE.name());
            }
            return this.updateById(distribution);
        }
        return false;
    }

    private boolean updateOrCreateAdapayMember(Map<String, Object> adapayMember, String memberId, Distribution distribution) {
        boolean isMemberUpdated = false;
        if (adapayMember != null && adapayMember.get("status").equals(AdapayTradeStatusEnum.succeeded.name())) {
            isMemberUpdated = adapayACHService.updateAdapayMember(memberId);
            if (!isMemberUpdated) {
                log.error("会员：{}，更新分账系统用户对象失败", memberId);
                throw new ServiceException(ResultCode.ADAPAY_UPDATE_MEMBER_ERROR);
            }
        } else {
            try {
                isMemberUpdated = adapayACHService.createAdapayMember(memberId);
            } catch (Exception e) {
                log.error("会员：{}，创建分账系统用户对象失败", memberId);
                throw new ServiceException(ResultCode.ADAPAY_CRTEATE_MEMBER_ERROR);
            }
            if (!isMemberUpdated) {
                log.error("会员：{}，创建分账系统用户对象失败", memberId);
                throw new ServiceException(ResultCode.ADAPAY_CRTEATE_MEMBER_ERROR);
            }
        }
        return isMemberUpdated;
    }
    

    @Override
    public boolean retreat(String id) {

        // 检查分销开关
        checkDistributionSetting();

        // 根据id获取分销员
        Distribution distribution = this.getById(id);
        if (Optional.ofNullable(distribution).isPresent()) {
            distribution.setDistributionStatus(DistributionStatusEnum.RETREAT.name());
            return this.updateById(distribution);
        }
        return false;
    }

    @Override
    public boolean resume(String id) {

        // 检查分销开关
        checkDistributionSetting();

        // 根据id获取分销员
        Distribution distribution = this.getById(id);
        if (Optional.ofNullable(distribution).isPresent()) {
            distribution.setDistributionStatus(DistributionStatusEnum.PASS.name());
            return this.updateById(distribution);
        }
        return false;
    }

    @Override
    public void bindingDistribution(String distributionId) {

        // 判断用户是否登录，未登录不能进行绑定
        if (UserContext.getCurrentUser() == null) {
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }
        // 储存分销关系时间
        Distribution distribution = this.getById(distributionId);
        if (distribution != null) {
            Setting setting = settingService.get(SettingEnum.DISTRIBUTION_SETTING.name());
            DistributionSetting distributionSetting = JSONUtil.toBean(setting.getSettingValue(),
                    DistributionSetting.class);
            cache.put(CachePrefix.DISTRIBUTION.getPrefix() + "_" + UserContext.getCurrentUser().getId(),
                    distribution.getId(), distributionSetting.getDistributionDay().longValue(), TimeUnit.DAYS);
        }

    }

    /**
     * 检查分销设置开关
     */
    @Override
    public void checkDistributionSetting() {
        // 获取分销是否开启
        Setting setting = settingService.get(SettingEnum.DISTRIBUTION_SETTING.name());
        DistributionSetting distributionSetting = JSONUtil.toBean(setting.getSettingValue(), DistributionSetting.class);
        if (Boolean.FALSE.equals(distributionSetting.getIsOpen())) {
            throw new ServiceException(ResultCode.DISTRIBUTION_CLOSE);
        }
    }

    @Override
    public void subRebate(Double canRebate, String distributionId, Double distributionOrderPrice) {
        this.baseMapper.subRebate(canRebate, distributionId, distributionOrderPrice);
    }

    @Override
    public void addRebate(Double rebate, String distributionId, Double distributionOrderPrice) {
        this.baseMapper.addRebate(rebate, distributionId, distributionOrderPrice);
    }

    @Override
    public void addCanRebate(Double rebate, String distributionId) {
        this.baseMapper.addCanRebate(rebate, distributionId);
    }

    @Override
    public void addCashRebate(Double rebate, String distributionId) {
        this.baseMapper.addCashRebate(rebate, distributionId);
    }

    @Override
    public void subCashRebate(Double rebate, String distributionId) {
        this.baseMapper.subCashRebate(rebate, distributionId);
    }

}