package cn.lili.modules.store.serviceimpl;

import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.UserEnums;
import cn.lili.modules.connect.entity.enums.SourceEnum;
import cn.lili.modules.connect.service.ConnectService;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dos.StoreConnect;
import cn.lili.modules.store.mapper.StoreConnectMapper;
import cn.lili.modules.store.service.StoreConnectService;
import cn.lili.modules.store.service.StoreService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 商家微信绑定信息 业务实现
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class StoreConnectServiceImpl extends ServiceImpl<StoreConnectMapper, StoreConnect> implements StoreConnectService {

    @Autowired
    private StoreService storeService;

    @Autowired
    private ConnectService connectService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindStoreWechat(String storeId, String unionId, String unionType) {
        // 获取店铺信息
        Store store = storeService.getById(storeId);
        if (store == null) {
            log.error("店铺不存在，storeId: {}", storeId);
            return false;
        }

        // 检查是否已经绑定
        StoreConnect existConnect = getByStoreIdAndType(storeId, unionType);
        if (existConnect != null) {
            // 更新绑定信息
            existConnect.setUnionId(unionId);
            existConnect.setEnableNotification(true);
            return this.updateById(existConnect);
        } else {
            // 创建新的绑定信息
            StoreConnect storeConnect = new StoreConnect(storeId, store.getStoreName(), 
                    store.getMemberId(), unionId, unionType);
            return this.save(storeConnect);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindStoreWechat(String storeId, String unionType) {
        LambdaQueryWrapper<StoreConnect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreConnect::getStoreId, storeId)
                   .eq(StoreConnect::getUnionType, unionType);
        return this.remove(queryWrapper);
    }

    @Override
    public StoreConnect getByStoreIdAndType(String storeId, String unionType) {
        LambdaQueryWrapper<StoreConnect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreConnect::getStoreId, storeId)
                   .eq(StoreConnect::getUnionType, unionType);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<StoreConnect> getByStoreId(String storeId) {
        LambdaQueryWrapper<StoreConnect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreConnect::getStoreId, storeId);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotificationStatus(String storeId, String unionType, Boolean enableNotification) {
        LambdaUpdateWrapper<StoreConnect> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StoreConnect::getStoreId, storeId)
                    .eq(StoreConnect::getUnionType, unionType)
                    .set(StoreConnect::getEnableNotification, enableNotification);
        return this.update(updateWrapper);
    }

    @Override
    public List<StoreConnect> getByMemberId(String memberId) {
        LambdaQueryWrapper<StoreConnect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreConnect::getMemberId, memberId);
        return this.list(queryWrapper);
    }
}
