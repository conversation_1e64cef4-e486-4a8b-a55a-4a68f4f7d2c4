package cn.lili.modules.store.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商家微信绑定结果
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@ApiModel(value = "商家微信绑定结果")
public class StoreWechatBindResult {

    @ApiModelProperty(value = "二维码URL")
    private String qrCodeUrl;

    @ApiModelProperty(value = "绑定令牌")
    private String bindToken;

    @ApiModelProperty(value = "绑定类型")
    private String unionType;

    @ApiModelProperty(value = "过期时间（秒）")
    private Long expireTime;

    @ApiModelProperty(value = "轮询间隔（毫秒）")
    private Long pollInterval;

    public StoreWechatBindResult() {
        this.expireTime = 300L; // 默认5分钟过期
        this.pollInterval = 2000L; // 默认2秒轮询一次
    }

    public StoreWechatBindResult(String qrCodeUrl, String bindToken, String unionType) {
        this();
        this.qrCodeUrl = qrCodeUrl;
        this.bindToken = bindToken;
        this.unionType = unionType;
    }
}
