package cn.lili.modules.store.service;

import cn.lili.modules.store.entity.dos.StoreConnect;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 商家微信绑定信息 业务层
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface StoreConnectService extends IService<StoreConnect> {

    /**
     * 绑定商家微信信息
     *
     * @param storeId   店铺ID
     * @param unionId   微信unionId
     * @param unionType 绑定类型
     * @return 绑定结果
     */
    boolean bindStoreWechat(String storeId, String unionId, String unionType);

    /**
     * 解绑商家微信信息
     *
     * @param storeId   店铺ID
     * @param unionType 绑定类型
     * @return 解绑结果
     */
    boolean unbindStoreWechat(String storeId, String unionType);

    /**
     * 根据店铺ID和绑定类型查询绑定信息
     *
     * @param storeId   店铺ID
     * @param unionType 绑定类型
     * @return 绑定信息
     */
    StoreConnect getByStoreIdAndType(String storeId, String unionType);

    /**
     * 根据店铺ID查询所有绑定信息
     *
     * @param storeId 店铺ID
     * @return 绑定信息列表
     */
    List<StoreConnect> getByStoreId(String storeId);

    /**
     * 启用/禁用商家微信通知
     *
     * @param storeId            店铺ID
     * @param unionType          绑定类型
     * @param enableNotification 是否启用通知
     * @return 操作结果
     */
    boolean updateNotificationStatus(String storeId, String unionType, Boolean enableNotification);

    /**
     * 根据会员ID获取商家微信绑定信息
     *
     * @param memberId 会员ID
     * @return 绑定信息列表
     */
    List<StoreConnect> getByMemberId(String memberId);
}
