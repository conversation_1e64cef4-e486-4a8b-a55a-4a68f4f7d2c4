package cn.lili.modules.store.serviceimpl;

import cn.hutool.core.util.IdUtil;
import cn.lili.cache.Cache;
import cn.lili.common.utils.QRCodeUtil;
import cn.lili.modules.connect.entity.dto.ConnectAuthUser;
import cn.lili.modules.connect.service.ConnectService;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dto.StoreWechatBindResult;
import cn.lili.modules.store.service.StoreConnectService;
import cn.lili.modules.store.service.StoreService;
import cn.lili.modules.store.service.StoreWechatBindService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 商家微信绑定服务实现
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class StoreWechatBindServiceImpl implements StoreWechatBindService {

    @Autowired
    private Cache cache;

    @Autowired
    private StoreService storeService;

    @Autowired
    private StoreConnectService storeConnectService;

    @Autowired
    private ConnectService connectService;

    @Value("${lili.domain.wechat:}")
    private String sellerFrontendDomain;

    /**
     * 绑定令牌缓存前缀
     */
    private static final String BIND_TOKEN_PREFIX = "STORE_WECHAT_BIND:";

    /**
     * 绑定状态枚举
     */
    public enum BindStatus {
        WAITING("等待扫码"),
        SCANNED("已扫码"),
        SUCCESS("绑定成功"),
        EXPIRED("已过期"),
        FAILED("绑定失败");

        private final String description;

        BindStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    @Override
    public StoreWechatBindResult generateWechatBindQRCode(String storeId, String memberId, String unionType) {
        // 生成唯一的绑定令牌
        String bindToken = IdUtil.simpleUUID();
        
        // 构建绑定信息
        StoreWechatBindInfo bindInfo = new StoreWechatBindInfo();
        bindInfo.setStoreId(storeId);
        bindInfo.setMemberId(memberId);
        bindInfo.setUnionType(unionType);
        bindInfo.setStatus(BindStatus.WAITING.name());
        bindInfo.setCreateTime(System.currentTimeMillis());

        // 缓存绑定信息，5分钟过期
        cache.put(BIND_TOKEN_PREFIX + bindToken, bindInfo, 300L, TimeUnit.SECONDS);

        // 生成二维码URL（指向前端绑定页面）
        String qrCodeContent = buildQRCodeContent(bindToken, unionType);
        String qrCodeUrl = QRCodeUtil.createQRCode(qrCodeContent);

        return new StoreWechatBindResult(qrCodeUrl, bindToken, unionType);
    }

    @Override
    public String checkBindStatus(String bindToken, String unionType) {
        StoreWechatBindInfo bindInfo = (StoreWechatBindInfo) cache.get(BIND_TOKEN_PREFIX + bindToken);
        if (bindInfo == null) {
            return BindStatus.EXPIRED.name();
        }
        return bindInfo.getStatus();
    }

    @Override
    public boolean processWechatBind(ConnectAuthUser authUser, String bindToken, String unionType) {
        try {
            // 获取绑定信息
            StoreWechatBindInfo bindInfo = (StoreWechatBindInfo) cache.get(BIND_TOKEN_PREFIX + bindToken);
            if (bindInfo == null) {
                log.error("绑定令牌已过期或不存在: {}", bindToken);
                return false;
            }

            // 更新状态为已扫码
            bindInfo.setStatus(BindStatus.SCANNED.name());
            cache.put(BIND_TOKEN_PREFIX + bindToken, bindInfo, 300L, TimeUnit.SECONDS);

            // 获取店铺信息
            Store store = storeService.getById(bindInfo.getStoreId());
            if (store == null) {
                log.error("店铺不存在: {}", bindInfo.getStoreId());
                bindInfo.setStatus(BindStatus.FAILED.name());
                cache.put(BIND_TOKEN_PREFIX + bindToken, bindInfo, 300L, TimeUnit.SECONDS);
                return false;
            }

            // 绑定到Connect表
            connectService.loginBindUser(bindInfo.getMemberId(), authUser.getUuid(), unionType);

            // 绑定到StoreConnect表
            boolean result = storeConnectService.bindStoreWechat(
                    bindInfo.getStoreId(), authUser.getUuid(), unionType);

            if (result) {
                bindInfo.setStatus(BindStatus.SUCCESS.name());
                log.info("商家微信绑定成功: storeId={}, unionType={}", bindInfo.getStoreId(), unionType);
            } else {
                bindInfo.setStatus(BindStatus.FAILED.name());
                log.error("商家微信绑定失败: storeId={}, unionType={}", bindInfo.getStoreId(), unionType);
            }

            cache.put(BIND_TOKEN_PREFIX + bindToken, bindInfo, 300L, TimeUnit.SECONDS);
            return result;

        } catch (Exception e) {
            log.error("处理微信绑定失败", e);
            return false;
        }
    }

    @Override
    public void cleanExpiredBindTokens() {
        // 这里可以实现清理过期令牌的逻辑
        // 由于使用了缓存的TTL机制，过期的令牌会自动清理
        log.debug("清理过期的绑定令牌");
    }

    /**
     * 构建二维码内容
     *
     * @param bindToken 绑定令牌
     * @param unionType 绑定类型
     * @return 二维码内容
     */
    private String buildQRCodeContent(String bindToken, String unionType) {
        // 获取商家端前端域名
        String frontendDomain = getSellerFrontendDomain();

        // 构建指向前端绑定页面的URL
        return String.format("%s/store-wechat-bind?token=%s&type=%s",
                frontendDomain, bindToken, unionType);
    }

    /**
     * 获取商家端前端域名
     * 注意：这里必须配置商家端前端域名，不是API域名
     *
     * @return 商家端前端域名
     */
    private String getSellerFrontendDomain() {
        if (sellerFrontendDomain != null && !sellerFrontendDomain.trim().isEmpty()) {
            return sellerFrontendDomain.trim();
        }

        // 如果没有配置域名，记录错误并返回提示信息
        log.error("未配置商家端前端域名 lili.domain.seller，请在配置文件中设置正确的商家端前端域名（不是API域名）");
        throw new RuntimeException("未配置商家端前端域名，请联系管理员配置 lili.domain.store 参数为商家端前端域名");
    }

    /**
     * 绑定信息内部类
     */
    public static class StoreWechatBindInfo {
        private String storeId;
        private String memberId;
        private String unionType;
        private String status;
        private Long createTime;

        // Getters and Setters
        public String getStoreId() { return storeId; }
        public void setStoreId(String storeId) { this.storeId = storeId; }
        public String getMemberId() { return memberId; }
        public void setMemberId(String memberId) { this.memberId = memberId; }
        public String getUnionType() { return unionType; }
        public void setUnionType(String unionType) { this.unionType = unionType; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public Long getCreateTime() { return createTime; }
        public void setCreateTime(Long createTime) { this.createTime = createTime; }
    }
}
