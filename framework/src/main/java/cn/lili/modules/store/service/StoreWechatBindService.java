package cn.lili.modules.store.service;

import cn.lili.modules.connect.entity.dto.ConnectAuthUser;
import cn.lili.modules.store.entity.dto.StoreWechatBindResult;

/**
 * 商家微信绑定服务
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface StoreWechatBindService {

    /**
     * 生成微信绑定二维码
     *
     * @param storeId   店铺ID
     * @param memberId  会员ID
     * @param unionType 绑定类型
     * @return 绑定结果
     */
    StoreWechatBindResult generateWechatBindQRCode(String storeId, String memberId, String unionType);

    /**
     * 检查绑定状态
     *
     * @param bindToken 绑定令牌
     * @param unionType 绑定类型
     * @return 绑定状态：WAITING-等待扫码，SCANNED-已扫码，SUCCESS-绑定成功，EXPIRED-已过期，FAILED-绑定失败
     */
    String checkBindStatus(String bindToken, String unionType);

    /**
     * 处理微信绑定
     *
     * @param authUser  微信用户信息
     * @param bindToken 绑定令牌
     * @param unionType 绑定类型
     * @return 绑定结果
     */
    boolean processWechatBind(ConnectAuthUser authUser, String bindToken, String unionType);

    /**
     * 清理过期的绑定令牌
     */
    void cleanExpiredBindTokens();
}
