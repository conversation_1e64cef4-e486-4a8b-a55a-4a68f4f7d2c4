package cn.lili.modules.store.entity.dos;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商家微信绑定信息
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@TableName("li_store_connect")
@ApiModel(value = "商家微信绑定信息")
@NoArgsConstructor
public class StoreConnect extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺ID")
    private String storeId;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "商家会员ID")
    private String memberId;

    @ApiModelProperty(value = "微信unionId")
    private String unionId;

    /**
     * @see cn.lili.modules.connect.entity.enums.SourceEnum
     */
    @ApiModelProperty(value = "绑定类型：WECHAT_OFFIACCOUNT_OPEN_ID-公众号，WECHAT_MP_OPEN_ID-小程序")
    private String unionType;

    @ApiModelProperty(value = "是否启用微信通知")
    private Boolean enableNotification;

    public StoreConnect(String storeId, String storeName, String memberId, String unionId, String unionType) {
        this.storeId = storeId;
        this.storeName = storeName;
        this.memberId = memberId;
        this.unionId = unionId;
        this.unionType = unionType;
        this.enableNotification = true;
    }
}
