package cn.lili.modules.system.serviceimpl;

import cn.hutool.json.JSONUtil;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.StoreWechatNotificationSetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.system.service.SettingService;
import cn.lili.modules.system.service.StoreWechatNotificationSettingService;
import cn.lili.modules.wechat.entity.enums.StoreMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;

/**
 * 商家微信通知设置 业务实现
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class StoreWechatNotificationSettingServiceImpl implements StoreWechatNotificationSettingService {

    @Autowired
    private SettingService settingService;



    @Override
    public StoreWechatNotificationSetting getStoreWechatNotificationSetting() {
        try {
            Setting setting = settingService.get(SettingEnum.STORE_WECHAT_NOTIFICATION_SETTING.name());
            if (setting != null && setting.getSettingValue() != null) {
                return JSONUtil.toBean(setting.getSettingValue(), StoreWechatNotificationSetting.class);
            }
        } catch (Exception e) {
            log.error("获取商家微信通知设置失败", e);
        }
        // 返回默认设置
        return new StoreWechatNotificationSetting();
    }

    @Override
    public boolean saveStoreWechatNotificationSetting(StoreWechatNotificationSetting setting) {
        try {
            Setting settingEntity = settingService.get(SettingEnum.STORE_WECHAT_NOTIFICATION_SETTING.name());
            if (settingEntity == null) {
                settingEntity = new Setting();
                settingEntity.setId(SettingEnum.STORE_WECHAT_NOTIFICATION_SETTING.name());
            }
            settingEntity.setSettingValue(JSONUtil.toJsonStr(setting));
            return settingService.saveUpdate(settingEntity);
        } catch (Exception e) {
            log.error("保存商家微信通知设置失败", e);
            return false;
        }
    }

    @Override
    public boolean isNotificationEnabled(String messageType) {
        StoreWechatNotificationSetting setting = getStoreWechatNotificationSetting();
        
        // 首先检查总开关
        if (!setting.getEnableStoreNotification()) {
            return false;
        }

        // 检查具体消息类型开关
        try {
            StoreMessageTypeEnum messageTypeEnum = StoreMessageTypeEnum.valueOf(messageType);
            switch (messageTypeEnum) {
                case ORDER_PAID:
                    return setting.getEnableOrderPaidNotification();
                case ORDER_DELIVERED:
                    return setting.getEnableOrderDeliveredNotification();
                case ORDER_COMPLETED:
                    return setting.getEnableOrderCompletedNotification();
                case ORDER_CANCELLED:
                    return setting.getEnableOrderCancelledNotification();
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("检查消息类型开关失败，messageType: {}", messageType, e);
            return false;
        }
    }

    @Override
    public boolean isInNotificationTimeRange() {
        StoreWechatNotificationSetting setting = getStoreWechatNotificationSetting();
        
        // 如果没有时间限制，则允许发送
        if (setting.getNotificationTimeLimit() == null || setting.getNotificationTimeLimit() == 0) {
            return true;
        }

        try {
            LocalTime now = LocalTime.now();
            int currentHour = now.getHour();
            
            int startHour = setting.getNotificationStartHour() != null ? setting.getNotificationStartHour() : 9;
            int endHour = setting.getNotificationEndHour() != null ? setting.getNotificationEndHour() : 22;
            
            // 检查当前时间是否在允许范围内
            if (startHour <= endHour) {
                // 正常时间范围，如 9-22
                return currentHour >= startHour && currentHour <= endHour;
            } else {
                // 跨天时间范围，如 22-9（表示22点到次日9点）
                return currentHour >= startHour || currentHour <= endHour;
            }
        } catch (Exception e) {
            log.error("检查通知时间范围失败", e);
            return true; // 出错时默认允许发送
        }
    }
}
