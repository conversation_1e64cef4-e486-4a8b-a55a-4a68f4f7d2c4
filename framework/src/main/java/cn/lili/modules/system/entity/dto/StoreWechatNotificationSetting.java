package cn.lili.modules.system.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商家微信通知设置
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@ApiModel(value = "商家微信通知设置")
public class StoreWechatNotificationSetting {

    @ApiModelProperty(value = "是否启用商家微信通知功能")
    private Boolean enableStoreNotification = true;

    @ApiModelProperty(value = "是否启用订单付款通知")
    private Boolean enableOrderPaidNotification = true;

    @ApiModelProperty(value = "是否启用订单发货通知")
    private Boolean enableOrderDeliveredNotification = false;

    @ApiModelProperty(value = "是否启用订单完成通知")
    private Boolean enableOrderCompletedNotification = false;

    @ApiModelProperty(value = "是否启用订单取消通知")
    private Boolean enableOrderCancelledNotification = false;

    @ApiModelProperty(value = "通知发送时间限制（小时），0表示不限制")
    private Integer notificationTimeLimit = 0;

    @ApiModelProperty(value = "通知发送开始时间（24小时制，如：9表示9点）")
    private Integer notificationStartHour = 9;

    @ApiModelProperty(value = "通知发送结束时间（24小时制，如：22表示22点）")
    private Integer notificationEndHour = 22;
}
