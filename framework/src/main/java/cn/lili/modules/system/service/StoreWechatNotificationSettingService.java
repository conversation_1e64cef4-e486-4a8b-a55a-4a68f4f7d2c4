package cn.lili.modules.system.service;

import cn.lili.modules.system.entity.dto.StoreWechatNotificationSetting;

/**
 * 商家微信通知设置 业务层
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface StoreWechatNotificationSettingService {

    /**
     * 获取商家微信通知设置
     *
     * @return 商家微信通知设置
     */
    StoreWechatNotificationSetting getStoreWechatNotificationSetting();

    /**
     * 保存商家微信通知设置
     *
     * @param setting 商家微信通知设置
     * @return 操作结果
     */
    boolean saveStoreWechatNotificationSetting(StoreWechatNotificationSetting setting);

    /**
     * 检查是否允许发送通知
     *
     * @param messageType 消息类型
     * @return 是否允许发送
     */
    boolean isNotificationEnabled(String messageType);

    /**
     * 检查当前时间是否在允许发送通知的时间范围内
     *
     * @return 是否在允许时间范围内
     */
    boolean isInNotificationTimeRange();
}
