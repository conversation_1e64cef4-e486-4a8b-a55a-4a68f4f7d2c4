package cn.lili.modules.adapay.serviceimpl;

import cn.lili.cache.Cache;
import cn.lili.modules.adapay.entity.dos.AdapayBanks;
import cn.lili.modules.adapay.mapper.AdapayBanksMapper;
import cn.lili.modules.adapay.service.AdapayBanksService;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 分账系统银行代码业务层实现
 *
 * <AUTHOR>
 * @since 2020/12/2 11:11
 */
@Service
public class AdapayBanksServiceImpl extends ServiceImpl<AdapayBanksMapper, AdapayBanks> implements AdapayBanksService {

    @Autowired
    private Cache cache;

    @Override
    public List<AdapayBanks> getItem() {
        List<AdapayBanks> regions = this.list();
        regions.sort(Comparator.comparing(AdapayBanks::getOrderNum));
        return regions;
    }

}