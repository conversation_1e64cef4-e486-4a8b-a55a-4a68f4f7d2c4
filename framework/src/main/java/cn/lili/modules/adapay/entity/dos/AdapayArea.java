package cn.lili.modules.adapay.entity.dos;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 分账系统行政地区
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@TableName("li_adapay_area")
@ApiModel(value = "分账系统行政地区")
public class AdapayArea extends BaseEntity {

    private static final long serialVersionUID = 418341656517240988L;

    @NotEmpty(message = "父id不能为空")
    @ApiModelProperty(value = "父id")
    private String parentId;

    @NotEmpty(message = "省份编码不能为空")
    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    @ApiModelProperty(value =
            "行政区划级别" +
                    "country:国家" +
                    "province:省份（直辖市会在province和city显示）" +
                    "city:市（直辖市会在province和city显示）")
    @NotEmpty(message = "品牌名称不能为空")
    private String level;

    @NotEmpty(message = "城市名称不能为空")
    @ApiModelProperty(value = "城市")
    private String cityName;

    @NotEmpty(message = "省份名称不能为空")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    @NotNull(message = "排序不能为空")
    @ApiModelProperty(value = "排序")
    private Integer orderNum;

}