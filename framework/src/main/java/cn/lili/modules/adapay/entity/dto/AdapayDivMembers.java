package cn.lili.modules.adapay.entity.dto;

import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分账对象信息
 *
 * <AUTHOR>
 * @since 2020/12/7 15:50
 */
@Data
public class AdapayDivMembers {    
    @Size(min = 0, max = 64)
    @ApiModelProperty(value = "分账用户 Member对象 的 id")
    private String memberId;
    
    @Size(min = 1, max = 14)
    @ApiModelProperty(value = "分账金额，精确到分，如0.50，1.00等，分账总金额必须等于主交易金额,金额不能为0.00")
    private String amount;
    
    @Size(min = 1, max = 1)
    @ApiModelProperty(value = "是否手续费承担方，N-否，Y-是，手续费承担方有且只能有一个")
    private String feeFlag;
    
    @Size(min = 1, max = 200)
    @ApiModelProperty(value = "应用的app_id")
    private String appId;
}
