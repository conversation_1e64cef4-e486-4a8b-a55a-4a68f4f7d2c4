package cn.lili.modules.adapay.service;

import cn.lili.modules.adapay.entity.dos.AdapayArea;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * 分账系统行政地区业务层
 *
 * <AUTHOR>
 * @since 2020/12/2 14:14
 */
@CacheConfig(cacheNames = "{adapayAreas}")
public interface AdapayAreaService extends IService<AdapayArea> {
    /**
     * 获取地区列表
     *
     * @param id 地区ID
     * @return 地区列表
     */
    @Cacheable(key = "#id")
    List<AdapayArea> getItem(String id);

    /**
     * 获取省份名称
     *
     * @param id 地区ID
     * @return 地区列表
     */
    @Cacheable(key = "#id")
    String getProvNameByCode(String code);

    /**
     * 获取城市名称
     *
     * @param id 地区ID
     * @return 地区列表
     */
    @Cacheable(key = "#id")
    String getCityNameByCode(String code);
}