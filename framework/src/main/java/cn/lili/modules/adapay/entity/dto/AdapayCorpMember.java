package cn.lili.modules.adapay.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 企业用户对象
 *
 * <AUTHOR>
 * @since 2020/12/7 15:50
 */
@Data
public class AdapayCorpMember {
    @Size(min = 1, max = 64)
    @NotBlank(message = "控制台主页面应用的app_id不能为空")
    @ApiModelProperty(value = "控制台主页面应用的app_id")
    private String appId;
    
    @Size(min = 1, max = 64)
    @NotBlank(message = "请求订单号不能为空")
    @ApiModelProperty(value = "请求订单号")
    private String orderNo;
    
    @Size(min = 1, max = 64)
    @NotBlank(message = "商户下的用户id不能为空")
    @ApiModelProperty(value = "商户下的用户id")
    private String memberId;
    
    @Size(min = 1, max = 50)
    @NotBlank(message = "企业名称不能为空")
    @ApiModelProperty(value = "企业名称")
    private String name;
    
    @Size(min = 1, max = 4)
    @NotBlank(message = "省份编码不能为空")
    @ApiModelProperty(value = "省份编码")
    private String provCode;
    
    @Size(min = 1, max = 4)
    @NotBlank(message = "地区编码不能为空")
    @ApiModelProperty(value = "地区编码")
    private String areaCode;
    
    @Size(min = 1, max = 18)
    @NotBlank(message = "统一社会信用码不能为空")
    @ApiModelProperty(value = "统一社会信用码")
    private String socialCreditCode;
    
    @Size(min = 1, max = 8)
    @NotBlank(message = "统一社会信用证有效期不能为空")
    @ApiModelProperty(value = "统一社会信用证有效期")
    private String socialCreditCodeExpires;
    
    @Size(min = 1, max = 200)
    @NotBlank(message = "经营范围不能为空")
    @ApiModelProperty(value = "经营范围")
    private String businessScope;
    
    @Size(min = 1, max = 20)
    @NotBlank(message = "法人姓名不能为空")
    @ApiModelProperty(value = "法人姓名")
    private String legalPerson;
    
    @Size(min = 1, max = 20)
    @NotBlank(message = "法人身份证号码不能为空")
    @ApiModelProperty(value = "法人身份证号码")
    private String legalCertId;
    
    @Size(min = 1, max = 8)
    @NotBlank(message = "法人身份证有效期不能为空")
    @ApiModelProperty(value = "法人身份证有效期")
    private String legalCertIdExpires;
    
    @Size(min = 1, max = 11)
    @NotBlank(message = "法人手机号不能为空")
    @ApiModelProperty(value = "法人手机号")
    private String legalMp;
    
    @Size(min = 1, max = 255)
    @NotBlank(message = "企业地址不能为空")
    @ApiModelProperty(value = "企业地址")
    private String address;
    
    @Size(max = 6)
    @ApiModelProperty(value = "邮编")
    private String zipCode;
    
    @Size(max = 30)
    @ApiModelProperty(value = "企业电话")
    private String telphone;
    
    @Size(max = 40)
    @ApiModelProperty(value = "企业邮箱")
    private String email;
    
    @Size(min = 1, max = 40)
    @NotBlank(message = "上传附件不能为空")
    @ApiModelProperty(value = "上传附件，传入的中文文件名称为 UTF-8 字符集 URLEncode 编码后的字符串。内容须包含三证合一证件照、法人身份证正面照、法人身份证反面照、开户银行许可证照。压缩 zip包后上传，最大限制为 9 M。")
    private String attachFile;
    
    @Size(max = 8)
    @ApiModelProperty(value = "银行代码，如果需要自动开结算账户，本字段必填（详见附录银行代码）")
    private String bankCode;
    
    @Size(max = 1)
    @ApiModelProperty(value = "银行账户类型：1-对公；2-对私，如果需要自动开结算账户，本字段必填")
    private String bankAcctType;
    
    @Size(max = 40)
    @ApiModelProperty(value = "银行卡号，如果需要自动开结算账户，本字段必填")
    private String cardNo;
    
    @Size(max = 64)
    @ApiModelProperty(value = "银行卡对应的户名，如果需要自动开结算账户，本字段必填；若银行账户类型是对公，必须与企业名称一致")
    private String cardName;
    
    @Size(max = 255)
    @ApiModelProperty(value = "异步通知地址，url为http/https路径，服务器POST回调，URL上请勿附带参数")
    private String notifyUrl;

    @NotBlank(message = "营业执照电子版不能为空")
    @ApiModelProperty(value = "营业执照电子版")
    private String licencePhoto;

    @NotBlank(message = "法人身份证不能为空")
    @ApiModelProperty(value = "法人身份证照片")
    private String legalPhoto;

    @NotBlank(message = "法人身份证国徽面不能为空")
    @ApiModelProperty(value = "法人身份证国徽面照片")
    private String legalPhoto2;


}
