package cn.lili.modules.adapay.serviceimpl;

import cn.lili.cache.Cache;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.adapay.entity.dos.AdapayArea;
import cn.lili.modules.adapay.mapper.AdapayAreaMapper;
import cn.lili.modules.adapay.service.AdapayAreaService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 分账系统行政地区业务层实现
 *
 * <AUTHOR>
 * @since 2020/12/2 11:11
 */
@Service
public class AdapayAreaServiceImpl extends ServiceImpl<AdapayAreaMapper, AdapayArea> implements AdapayAreaService {

    @Autowired
    private Cache cache;

    @Override
    public List<AdapayArea> getItem(String id) {
        LambdaQueryWrapper<AdapayArea> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AdapayArea::getParentId, id);
        List<AdapayArea> regions = this.list(lambdaQueryWrapper);
        regions.sort(Comparator.comparing(AdapayArea::getOrderNum));
        return regions;
    }

    @Override
    public String getProvNameByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            throw new ServiceException(ResultCode.CODE_NOT_EXIST_ERROR);
        }
    
        String cacheKey = "provName:" + code;
        String provName = cache.getString(cacheKey);
    
        if (provName == null) {
            synchronized (this) {
                provName = cache.getString(cacheKey); // Double-check locking
                if (provName == null) {
                    try {
                        LambdaQueryWrapper<AdapayArea> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                        lambdaQueryWrapper.eq(AdapayArea::getCityCode, code);
                        AdapayArea adapayArea = this.getOne(lambdaQueryWrapper);
    
                        if (adapayArea != null) {
                            provName = adapayArea.getCityName();
                            cache.put(cacheKey, provName); // Update cache
                        } else {
                            provName = "Unknown"; // 或者根据业务需求处理
                        }
                    } catch (Exception e) {
                        // Log the exception and handle it appropriately
                        log.error("Error fetching city name by code: " + code, e);
                        throw new ServiceException(ResultCode.CODE_NOT_EXIST_ERROR);
                    }
                }
            }
        }
        return provName;
    }

    @Override
    public String getCityNameByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            throw new ServiceException(ResultCode.CODE_NOT_EXIST_ERROR);
        }
    
        String cacheKey = "cityName:" + code;
        String cityName = cache.getString(cacheKey);
    
        if (cityName == null) {
            synchronized (this) {
                cityName = cache.getString(cacheKey); // Double-check locking
                if (cityName == null) {
                    try {
                        LambdaQueryWrapper<AdapayArea> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                        lambdaQueryWrapper.eq(AdapayArea::getCityCode, code);
                        AdapayArea adapayArea = this.getOne(lambdaQueryWrapper);
    
                        if (adapayArea != null) {
                            cityName = adapayArea.getCityName();
                            cache.put(cacheKey, cityName); // Update cache
                        } else {
                            cityName = "Unknown"; // 或者根据业务需求处理
                        }
                    } catch (Exception e) {
                        // Log the exception and handle it appropriately
                        log.error("Error fetching city name by code: " + code, e);
                        throw new ServiceException(ResultCode.CODE_NOT_EXIST_ERROR);
                    }
                }
            }
        }
        return cityName;
    }
}