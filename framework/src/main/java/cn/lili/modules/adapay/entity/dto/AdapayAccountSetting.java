package cn.lili.modules.adapay.entity.dto;

import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AccountInfo结算对象
 *
 * <AUTHOR>
 * @since 2020/12/7 15:50
 */
@Data
public class AdapayAccountSetting {    
    @Size(min = 0, max = 64)
    @ApiModelProperty(value = "Adapay系统返回的结算账户id，若为商户本身时，不传该值")
    private String settleAccountId;
    
    @Size(min = 1, max = 16)
    @ApiModelProperty(value = "结算起始金额（0.00格式，整数部分最长13位，小数部分最长2位）")
    private String minAmt;
    
    @Size(min = 1, max = 16)
    @ApiModelProperty(value = "结算留存金额（0.00格式，整数部分最长13位，小数部分最长2位）")
    private String remainedAmt;
    
    @Size(min = 1, max = 200)
    @ApiModelProperty(value = "结算信息摘要，银行出款时摘要信息")
    private String channelRemark;
}
