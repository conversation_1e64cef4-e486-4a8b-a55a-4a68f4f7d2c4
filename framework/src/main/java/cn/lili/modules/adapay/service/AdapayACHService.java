package cn.lili.modules.adapay.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import cn.lili.modules.adapay.entity.dto.AdapayAccountInfo;
import cn.lili.modules.adapay.entity.dto.AdapayAccountSetting;
import cn.lili.modules.adapay.entity.dto.AdapayCorpMember;

public interface AdapayACHService {

    /**
     * 创建结算账户
     *
     * @return
     */
    Map<String, Object> createAdapaySettleAccount(String memberId, AdapayAccountInfo adapayAccountInfo);

    /**
     * 查询结算账户
     * 
     * @return
     */
    Map<String, Object> queryAdapaySettleAccount(String memberId, String settleAccountId);

    /**
     * 修改结算账户配置
     *
     * @return
     */
    Map<String, Object> modifyAdapaySettleAccountSetting(String memberId, AdapayAccountSetting accountSetting);

    /**
     * 删除结算账户
     *
     * @return
     */
    boolean deleteAdapaySettleAccount(String memberId, String settleAccountId);

    /**
     * 创建用户
     *
     * @return
     */
    boolean createAdapayMember(String memberId);

    /**
     * 更新用户对象
     *
     * @return
     */
    boolean updateAdapayMember(String memberId);

    /**
     * 创建用户对象
     *
     * @return
     */
    boolean createAdapayRealNameMember(cn.lili.modules.member.entity.dos.Member member);

    /**
     * 查询用户对象列表
     *
     * @return
     */
    Map<String, Object> queryAdapayMemberList(String pageIndex, String pageSize, String createdGte,
            String createdLte);

    /**
     * 查询用户对象
     *
     * @return
     */
    Map<String, Object> queryAdapayMember(String memberId);

    /**
     * 创建企业用户对象
     *
     * @return
     */
    boolean createAdapayCorpMember(AdapayCorpMember CorpMember);

    /**
     * 更新企业用户对象
     *
     * @return
     */
    boolean updateAdapayCorpMember(AdapayCorpMember CorpMember);

    /**
     * 查询企业用户对象
     *
     * @return
     */
    Map<String, Object> queryAdapayCorpMember(String memberId);

    /**
     * 异步通知回调
     *
     */
    void callback(HttpServletRequest request);
}
