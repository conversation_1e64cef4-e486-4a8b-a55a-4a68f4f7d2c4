package cn.lili.modules.adapay.serviceimpl;

import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.BeanUtil;
import cn.lili.modules.adapay.entity.dto.AdapayAccountInfo;
import cn.lili.modules.adapay.entity.dto.AdapayAccountSetting;
import cn.lili.modules.adapay.entity.dto.AdapayCorpMember;
import cn.lili.modules.adapay.entity.enums.AdapayCertTypeEnum;
import cn.lili.modules.adapay.entity.enums.AdapayTradeStatusEnum;
import cn.lili.modules.adapay.service.AdapayACHService;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.service.StoreService;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.AdapaySetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.system.service.SettingService;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huifu.adapay.Adapay;
import com.huifu.adapay.core.AdapayCore;
import com.huifu.adapay.core.util.AdapaySign;
import com.huifu.adapay.model.AdapayCommon;
import com.huifu.adapay.model.CorpMember;
import com.huifu.adapay.model.Member;
import com.huifu.adapay.model.MerConfig;
import com.huifu.adapay.model.SettleAccount;

@Slf4j
@Service
public class AdapayACHServiceImpl implements AdapayACHService {

    @Autowired
    private StoreService storeService;
    @Autowired
    private SettingService settingService;

    private String APP_ID;
    private static final long MAX_ZIP_SIZE = 9 * 1024 * 1024; // 9MB in bytes
    private String NOTIFY_URL;

    @PostConstruct
    public void init() {
        try {
            initMerchant();
            log.info("自动分账系统配置初始化成功");
        } catch (Exception e) {
            log.error("自动分账系统初始化配置失败", e);
            throw new ServiceException(ResultCode.ADAPAY_SETTING_INIT_ERROR);
        }
    }

    public void initMerchant() throws Exception {
        log.info("开始初始化自动分账系统配置");

        // 获取分账系统配置
        Setting setting = settingService.get(SettingEnum.ADAPAY_SETTING.name());
        if (setting == null) {
            log.error("未找到自动分账系统初始化配置参数");
            throw new ServiceException(ResultCode.ADAPAY_SETTING_NOT_FOUND);
        }
        AdapaySetting adapaySetting = JSONUtil.toBean(setting.getSettingValue(), AdapaySetting.class);

        // 设置AppId
        this.APP_ID = adapaySetting.getAppId();
        this.NOTIFY_URL = adapaySetting.getNotifyUrl();

        // debug 模式，开启后有详细的日志
        Adapay.debug = adapaySetting.getDebug();

        // prodMode 模式，默认为生产模式，false可以使用mock模式
        Adapay.prodMode = adapaySetting.getProdMode();
        log.info("获取到的自动分账系统初始化配置: {}", JSON.toJSONString(adapaySetting));
        /**
         * 初始化配置，服务器启动前，必须通过该方式初始化商户配置完成 apiKey为prod模式的API KEY mockApiKey为mock模式的API KEY
         * rsaPrivateKey为商户发起请求时，用于请求参数加签所需要的RSA私钥
         */
        String apiKey = adapaySetting.getApiKey();
        String mockApiKey = adapaySetting.getMockApiKey();
        String rsaPrivateKey = adapaySetting.getRsaPrivateKey();
        MerConfig merConfig = new MerConfig();
        merConfig.setApiKey(apiKey);
        merConfig.setApiMockKey(mockApiKey);
        merConfig.setRSAPrivateKey(rsaPrivateKey);
        Adapay.initWithMerConfig(merConfig);

        log.info("自动分账系统初始化配置完成");
    }

    /**
     * 创建结算账户对象
     *
     * @return
     */
    @Override
    public Map<String, Object> createAdapaySettleAccount(String memberId, AdapayAccountInfo adapayAccountInfo) {
        if (memberId == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        if (adapayAccountInfo == null) {
            throw new ServiceException(ResultCode.ADAPAY_SETTLE_ACCOUNT_NOT_EXIST);
        }
        Map<String, Object> settleAccountParams = new HashMap<String, Object>(4);
        Map<String, Object> accountInfo = new HashMap<String, Object>(9);
        accountInfo.put("card_id", adapayAccountInfo.getCardId());
        accountInfo.put("card_name", adapayAccountInfo.getCardName());
        accountInfo.put("cert_id", adapayAccountInfo.getCertId());
        accountInfo.put("tel_no", adapayAccountInfo.getTelNo());
        accountInfo.put("bank_code", adapayAccountInfo.getBankCode());
        accountInfo.put("bank_acct_type", adapayAccountInfo.getBankAcctType());
        accountInfo.put("cert_type", adapayAccountInfo.getCertType());
        accountInfo.put("prov_code", adapayAccountInfo.getProvCode());
        accountInfo.put("area_code", adapayAccountInfo.getAreaCode());
        settleAccountParams.put("member_id", memberId);
        settleAccountParams.put("app_id", APP_ID);
        settleAccountParams.put("channel", "bank_account");// 只支持bank_account（银行卡）
        settleAccountParams.put("account_info", accountInfo);

        try {
            Map<String, Object> settleCount = SettleAccount.create(settleAccountParams);
            log.info("创建结算账户，返回参数：{}", JSON.toJSONString(settleCount));
            log.info("=======execute Create settleCount end=======");
            return settleCount;
        } catch (Exception e) {
            log.error("创建结算账户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_CREATE_SETTLE_ACCOUNT_ERROR);
        }
    }

    /**
     * 查询企业结算账户
     *
     * @return
     */
    @Override
    public Map<String, Object> queryAdapaySettleAccount(String memberId, String settleAccountId) {
        Map<String, Object> settleAccountParams = new  HashMap<String, Object>(3);
        settleAccountParams.put("settle_account_id", settleAccountId);
        settleAccountParams.put("member_id", memberId);
        settleAccountParams.put("app_id", APP_ID);
        try {
            Map<String, Object> settleAccount = Member.query(settleAccountParams);
            log.info("查询结算账户，返回参数：{}", JSON.toJSONString(settleAccount));
            log.info("=======execute Query settleAccount end=======");

            return settleAccount;
        } catch (Exception e) {
            log.error("查询结算账户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_QUERY_SETTLE_ACCOUNT_ERROR);
        }
    }

    /**
     * 删除企业结算账户
     *
     * @return
     */
    @Override
    public boolean deleteAdapaySettleAccount(String memberId, String settleAccountId) {
        Map<String, Object> settleAccountParams = new  HashMap<String, Object>(3);
        settleAccountParams.put("settle_account_id", settleAccountId);
        settleAccountParams.put("member_id", memberId);
        settleAccountParams.put("app_id", APP_ID);
        try {
            Map<String, Object> settleAccount = SettleAccount.delete(settleAccountParams);
            log.info("删除结算账户，返回参数：{}", JSON.toJSONString(settleAccount));
            log.info("=======execute Delete settleAccount end=======");

            return true;
        } catch (Exception e) {
            log.error("删除结算账户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_DELETE_SETTLE_ACCOUNT_LIST_ERROR);
        }
    }

    /**
     * 修改企业结算账户配置
     *
     * @return
     */
    @Override
    public Map<String, Object> modifyAdapaySettleAccountSetting(String memberId, AdapayAccountSetting accountSetting) {
        Map<String, Object> accountSettingParams = new  HashMap<String, Object>(3);
        accountSettingParams.put("settle_account_id", accountSetting.getSettleAccountId());
        accountSettingParams.put("member_id", memberId);
        accountSettingParams.put("app_id", APP_ID);
        accountSettingParams.put("min_amt", accountSetting.getMinAmt());
        accountSettingParams.put("remained_amt", accountSetting.getRemainedAmt());
        accountSettingParams.put("channel_remark", accountSetting.getChannelRemark());
        try {
            Map<String, Object> settleAccount = Member.query(accountSettingParams);
            log.info("修改结算账户配置，返回参数：{}", JSON.toJSONString(settleAccount));
            log.info("=======execute Modify settleAccountSetting end=======");

            return settleAccount;
        } catch (Exception e) {
            log.error("修改结算账户配置失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_MODIFY_SETTLE_ACCOUNT_SETTING_ERROR);
        }
    }

    /**
     * 创建用户对象
     *
     * @return
     */
    @Override
    public boolean createAdapayMember(String memberId) {
        if (memberId == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        Map<String, Object> memberParams = new HashMap<String, Object>(2);
        memberParams.put("member_id", memberId);
        memberParams.put("app_id", APP_ID);
        // memberParams.put("location", member.getRegion());
        // memberParams.put("email", member.getEmail());

        // 处理性别赋值
        // int sex = member.getSex();
        // memberParams.put("gender", getGender(sex));

        // memberParams.put("nickname", member.getNickName());
        try {
            Map<String, Object> adapayMember = Member.create(memberParams);
            log.info("创建用户，返回参数：{}", JSON.toJSONString(adapayMember));
            log.info("=======execute Create Member end=======");
            return true;
        } catch (Exception e) {
            log.error("创建用户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_CRTEATE_MEMBER_ERROR);
        }
    }

    /**
     * 更新用户对象
     *
     * @return
     */
    @Override
    public boolean updateAdapayMember(String memberId) {
        // if (member == null) {
        //     throw new ServiceException(ResultCode.USER_NOT_EXIST);
        // }
        Map<String, Object> memberParams = new HashMap<String, Object>(2);
        memberParams.put("member_id", memberId);
        memberParams.put("app_id", APP_ID);
        // memberParams.put("location", member.getRegion());
        // memberParams.put("email", member.getEmail());

        // 处理性别赋值
        // int sex = member.getSex();
        // memberParams.put("gender", getGender(sex));

        // memberParams.put("nickname", member.getNickName());
        try {
            Map<String, Object> adapayMember = Member.update(memberParams);
            log.info("更新用户{}，返回参数：{}", memberId, JSON.toJSONString(adapayMember));
            log.info("=======execute Update Member end=======");
            return true;
        } catch (Exception e) {
            log.error("更新用户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_UPDATE_MEMBER_ERROR);
        }
    }

    private String getGender(int sex) {
        if (sex == 1) {
            return "MALE";
        } else if (sex == 0) {
            return "FEMALE";
        } else {
            return null;
        }
    }

    /**
     * 创建实名用户对象
     *
     * @return
     */
    @Override
    public boolean createAdapayRealNameMember(cn.lili.modules.member.entity.dos.Member member) {
        if (member == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        Map<String, Object> memberParams = new HashMap<String, Object>(10);
        memberParams.put("member_id", member.getId());
        memberParams.put("app_id", APP_ID);
        memberParams.put("tel_no", member.getMobile());
        memberParams.put("user_name", member.getRealName());
        memberParams.put("cert_type", AdapayCertTypeEnum.IDCARD.getCode());// 只支持：00-身份证
        memberParams.put("cert_id", member.getCertId());

        memberParams.put("location", member.getRegion());
        memberParams.put("email", member.getEmail());
        // 处理性别赋值
        int sex = member.getSex();
        memberParams.put("gender", getGender(sex));
        memberParams.put("nickname", member.getNickName());
        try {
            Map<String, Object> adapayMember = Member.create(memberParams);
            log.info("创建实名用户，返回参数：{}", JSON.toJSONString(adapayMember));
            log.info("=======execute Create RealName Member end=======");
            return true;
        } catch (Exception e) {
            log.error("创建实名用户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_CRTEATE_REALNAME_MEMBER_ERROR);
        }
    }

    /**
     * 查询用户对象
     *
     * @return
     */
    @Override
    public Map<String, Object> queryAdapayMember(String memberId) {
        Map<String, Object> memberParams = new HashMap<String, Object>(2);
        memberParams.put("member_id", memberId);
        memberParams.put("app_id", APP_ID);
        try {
            Map<String, Object> member = Member.query(memberParams);
            log.info("查询用户，返回参数：{}", JSON.toJSONString(member));
            log.info("=======execute Query Member end=======");

            return member;
        } catch (Exception e) {
            log.error("查询用户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_UPDATE_CORP_ERROR);
        }
    }

    /**
     * 查询用户对象
     *
     * @return
     */
    @Override
    public Map<String, Object> queryAdapayMemberList(String pageIndex, String pageSize, String createdGte,
            String createdLte) {
        Map<String, Object> memberParams = new HashMap<String, Object>(5);
        memberParams.put("page_index", pageIndex);
        memberParams.put("page_size", pageSize);
        memberParams.put("created_gte", createdGte);
        memberParams.put("created_lte", createdLte);
        memberParams.put("app_id", APP_ID);
        try {
            Map<String, Object> member = Member.queryList(memberParams);
            log.info("查询用户列表，返回参数：{}", JSON.toJSONString(member));
            log.info("=======execute Query MemberList end=======");

            return member;
        } catch (Exception e) {
            log.error("查询用户列表失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_QUERY_MEMBER_LIST_ERROR);
        }
    }

    /**
     * 创建企业用户对象
     *
     * @return
     */
    @Override
    public boolean createAdapayCorpMember(AdapayCorpMember corpMemberDTO) {

        Map<String, Object> memberParams = new HashMap<String, Object>(19);
        BeanUtil.copyProperties(corpMemberDTO, memberParams);
        memberParams.put("app_id", APP_ID);
        memberParams.put("order_no", "jsdk_order_" + System.currentTimeMillis());
        memberParams.put("member_id", corpMemberDTO.getMemberId());
        memberParams.put("name", corpMemberDTO.getName());
        memberParams.put("notify_url", NOTIFY_URL);
        memberParams.put("prov_code", corpMemberDTO.getProvCode());
        memberParams.put("area_code", corpMemberDTO.getAreaCode());
        memberParams.put("social_credit_code", corpMemberDTO.getSocialCreditCode());
        memberParams.put("social_credit_code_expires", corpMemberDTO.getSocialCreditCodeExpires());
        memberParams.put("business_scope", corpMemberDTO.getBusinessScope());
        memberParams.put("legal_person", corpMemberDTO.getLegalPerson());
        memberParams.put("legal_cert_id", corpMemberDTO.getLegalCertId());
        memberParams.put("legal_cert_id_expires", corpMemberDTO.getLegalCertIdExpires());
        memberParams.put("legal_mp", corpMemberDTO.getLegalMp());
        memberParams.put("address", corpMemberDTO.getAddress());
        memberParams.put("zip_code", corpMemberDTO.getZipCode());
        memberParams.put("telphone", corpMemberDTO.getTelphone());
        memberParams.put("email", corpMemberDTO.getEmail());

        String[] imageUrls = { corpMemberDTO.getLegalPhoto(), corpMemberDTO.getLegalPhoto2(),
                corpMemberDTO.getLicencePhoto() };

        String zipFilePath = "/var/lib/docker/home/<USER>/api/logs/uploaded_files_" + System.currentTimeMillis()
                + ".zip";
        try {
            createZipFromUrls(imageUrls, zipFilePath);
        } catch (IOException e) {
            log.error("创建ZIP文件失败", e);
            throw new ServiceException(ResultCode.ADAPAY_CREATE_CORP_ERROR);
        }

        File file = new File(zipFilePath);
        log.info("创建企业用户，请求参数：" + JSON.toJSONString(memberParams));
        memberParams.put("attach_file", corpMemberDTO.getAttachFile());

        try {
            Map<String, Object> member = CorpMember.create(memberParams, file);
            if (member != null && member.get("status").equals(AdapayTradeStatusEnum.pending.name())) {
                log.info("创建企业用户，返回参数：{}", JSON.toJSONString(member));
                log.info("=======execute Create CorpMember end=======");

                // 删除ZIP文件
                if (file.exists()) {
                    boolean isDeleted = file.delete();
                    if (isDeleted) {
                        log.info("ZIP文件已成功删除: {}", zipFilePath);
                    } else {
                        log.warn("无法删除ZIP文件: {}", zipFilePath);
                    }
                }
                return true;
            } else {
                log.error("创建企业用户失败，错误信息：{}", member.get("message"));
                throw new ServiceException(ResultCode.ADAPAY_CREATE_CORP_ERROR);
            }
        } catch (Exception e) {
            log.error("创建企业用户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_CREATE_CORP_ERROR);
        }
    }

    /**
     * 更新企业用户对象
     *
     * @return
     */
    @Override
    public boolean updateAdapayCorpMember(AdapayCorpMember corpMemberDTO) {

        Map<String, Object> memberParams = new HashMap<String, Object>(19);
        BeanUtil.copyProperties(corpMemberDTO, memberParams);
        memberParams.put("adapay_func_code", "corp_members.update");
        memberParams.put("app_id", APP_ID);
        memberParams.put("order_no", "jsdk_order_" + System.currentTimeMillis());
        memberParams.put("member_id", corpMemberDTO.getMemberId());
        memberParams.put("name", corpMemberDTO.getName());
        memberParams.put("prov_code", corpMemberDTO.getAreaCode());
        memberParams.put("area_code", corpMemberDTO.getAreaCode());
        memberParams.put("social_credit_code_expires", corpMemberDTO.getSocialCreditCodeExpires());
        memberParams.put("business_scope", corpMemberDTO.getBusinessScope());
        memberParams.put("legal_person", corpMemberDTO.getLegalPerson());
        memberParams.put("legal_cert_id", corpMemberDTO.getLegalCertId());
        memberParams.put("legal_cert_id_expires", corpMemberDTO.getLegalCertIdExpires());
        memberParams.put("legal_mp", corpMemberDTO.getLegalMp());
        memberParams.put("address", corpMemberDTO.getAddress());
        memberParams.put("zip_code", corpMemberDTO.getZipCode());
        memberParams.put("telphone", corpMemberDTO.getTelphone());
        memberParams.put("email", corpMemberDTO.getEmail());
        memberParams.put("notify_url", NOTIFY_URL);

        String[] imageUrls = { corpMemberDTO.getLegalPhoto(), corpMemberDTO.getLegalPhoto2(),
                corpMemberDTO.getLicencePhoto() };

        String zipFilePath = "/var/lib/docker/home/<USER>/api/logs/uploaded_files_" + System.currentTimeMillis()
                + ".zip";
        try {
            createZipFromUrls(imageUrls, zipFilePath);
        } catch (IOException e) {
            log.error("创建ZIP文件失败", e);
            throw new ServiceException(ResultCode.ADAPAY_CREATE_CORP_ERROR);
        }

        File file = new File(zipFilePath);
        memberParams.put("attach_file", corpMemberDTO.getAttachFile());
        log.info("更新企业用户，请求参数：" + JSON.toJSONString(memberParams));

        try {
            Map<String, Object> member = AdapayCommon.requestAdapayFile(memberParams, file);
            if (member != null && member.get("status").equals(AdapayTradeStatusEnum.succeeded.name())) {
                log.info("更新企业用户，返回参数：{}", JSON.toJSONString(member));
                log.info("=======execute Update CorpMember end=======");

                // 删除ZIP文件
                if (file.exists()) {
                    boolean isDeleted = file.delete();
                    if (isDeleted) {
                        log.info("ZIP文件已成功删除: {}", zipFilePath);
                    } else {
                        log.warn("无法删除ZIP文件: {}", zipFilePath);
                    }
                }
                return true;
            } else {
                log.error("更新企业用户失败，错误信息：{}", member.get("message"));
                throw new ServiceException(ResultCode.ADAPAY_UPDATE_CORP_ERROR);
            }
        } catch (Exception e) {
            log.error("更新企业用户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_UPDATE_CORP_ERROR);
        }
    }

    /**
     * 查询企业用户对象
     *
     * @return
     */
    @Override
    public Map<String, Object> queryAdapayCorpMember(String memberId) {

        Map<String, Object> memberParams = new HashMap<String, Object>(2);
        memberParams.put("member_id", memberId);
        memberParams.put("app_id", APP_ID);

        try {
            Map<String, Object> member = CorpMember.query(memberParams);
            log.info("查询企业用户，返回参数：{}", JSON.toJSONString(member));
            log.info("=======execute Create CorpMember end=======");

            return member;
        } catch (Exception e) {
            log.error("查询企业用户失败，错误信息：{}", e.getMessage(), e);
            throw new ServiceException(ResultCode.ADAPAY_QUERY_CORP_ERROR);
        }
    }

    /**
     * 异步通知回调
     *
     */
    @Override
    public void callback(HttpServletRequest request) {
        log.info("自动分账系统异步通知");
        try {
            // 获取参数
            String data = request.getParameter("data");
            String sign = request.getParameter("sign");
            String publicKey = AdapayCore.PUBLIC_KEY;

            // 验签
            boolean checkSign = AdapaySign.verifySign(data, sign, publicKey);
            if (!checkSign) {
                log.error("验签失败");
                return;
            }

            // 解析 data 为 JSON 对象
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(data);
            String type = rootNode.path("type").asText();

            // 处理不同的事件类型
            switch (type) {
            case "payment.succeeded":
                handlePaymentSucceeded(rootNode);
                break;
            case "payment.failed":
                handlePaymentFailed(rootNode);
                break;
            case "corp_member.succeeded":
                handleCorpMemberSucceeded(rootNode);
                break;
            case "corp_member.failed":
                handleCorpMemberFailed(rootNode);
                break;
            // 其他事件类型...
            default:
                log.warn("未知事件类型: {}", type);
                break;
            }
            return;
        } catch (Exception e) {
            log.error("异步回调处理异常", e);
            return;
        }
    }

    private void handlePaymentSucceeded(JsonNode rootNode) {
        // 处理支付成功的逻辑
        log.info("支付成功: {}", rootNode.toString());
    }

    private void handlePaymentFailed(JsonNode rootNode) {
        // 处理支付失败的逻辑
        log.info("支付失败: {}", rootNode.toString());
    }

    private void handleCorpMemberSucceeded(JsonNode rootNode) {
        handleCorpMember(rootNode, "企业用户创建成功");
    }

    private void handleCorpMemberFailed(JsonNode rootNode) {
        handleCorpMember(rootNode, "企业用户创建失败");
    }

    private void handleCorpMember(JsonNode rootNode, String logMessage) {
        // 处理企业用户创建成功的逻辑
        log.info("{}: {}", logMessage, rootNode.toString());

        // 提取 memberId
        JsonNode dataNode = rootNode.path("data");
        if (dataNode.isMissingNode() || !dataNode.has("member_id")) {
            log.error("member_id 未找到, {}", logMessage);
            return;
        }
        String memberId = dataNode.path("member_id").asText();

        // 提取 audit_desc
        if (dataNode.isMissingNode() || !dataNode.has("audit_desc")) {
            log.error("audit_desc 未找到, {}", logMessage);
            return;
        }
        String auditDesc = dataNode.path("audit_desc").asText();

        // 调用 storeService 更新 store.storeAdapayStatus
        try {
            Store store = storeService.getById(memberId);
            store.setStoreAdapayStatus(auditDesc);
            storeService.updateById(store);
        } catch (Exception e) {
            log.error("更新企业用户开户状态失败, memberId: {}, audit_desc: {}, error: {}", memberId, auditDesc, e);
        }
    }

    private static String encodeChineseInPath(String filePath) {
        int lastSlashIndex = filePath.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            return filePath; // 如果没有找到斜杠，说明路径中没有文件名，直接返回原路径
        }

        String directoryPart = filePath.substring(0, lastSlashIndex + 1);
        String fileNamePart = filePath.substring(lastSlashIndex + 1);

        try {
            String encodedFileNamePart = URLEncoder.encode(fileNamePart, "UTF-8");
            return directoryPart + encodedFileNamePart;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return filePath; // 如果编码失败，返回原路径
        }
    }

    private static void createZipFromUrls(String[] urls, String zipFilePath) throws IOException {
        FileOutputStream fos = new FileOutputStream(zipFilePath);
        ZipOutputStream zos = new ZipOutputStream(fos);
        byte[] buffer = new byte[1024];
        long totalSize = 0;
        Set<String> uniqueFileNames = new HashSet<>();

        for (String url : urls) {
            if (url == null || url.trim().isEmpty()) {
                log.error("无效的 URL: {}", url);
                zos.close();
                fos.close();
                Files.delete(Paths.get(zipFilePath));
                throw new IllegalArgumentException("无效的 URL: " + url);
            }

            try {
                URL imageUrl = new URL(url);
                HttpURLConnection httpConn = (HttpURLConnection) imageUrl.openConnection();
                int responseCode = httpConn.getResponseCode();

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    String disposition = httpConn.getHeaderField("Content-Disposition");
                    String contentType = httpConn.getContentType();
                    int contentLength = httpConn.getContentLength();

                    InputStream inputStream = httpConn.getInputStream();
                    String fileName = Paths.get(new URL(url).getPath()).getFileName().toString();

                    // 确保文件名唯一
                    String uniqueFileName = getUniqueFileName(uniqueFileNames, fileName);

                    ZipEntry zipEntry = new ZipEntry(uniqueFileName);
                    zos.putNextEntry(zipEntry);

                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) >= 0) {
                        zos.write(buffer, 0, bytesRead);
                        totalSize += bytesRead;
                        if (totalSize > MAX_ZIP_SIZE) {
                            zos.closeEntry();
                            zos.close();
                            fos.close();
                            Files.delete(Paths.get(zipFilePath));
                            throw new IOException("生成的ZIP文件大小超过9MB");
                        }
                    }

                    zos.closeEntry();
                    inputStream.close();
                } else {
                    log.error("无法下载文件: " + url);
                    zos.close();
                    fos.close();
                    Files.delete(Paths.get(zipFilePath));
                    throw new IOException("无法下载文件: " + url);
                }
            } catch (MalformedURLException e) {
                log.error("无效的 URL: {}", url, e);
                zos.close();
                fos.close();
                Files.delete(Paths.get(zipFilePath));
                throw new IllegalArgumentException("无效的 URL: " + url, e);
            } catch (IOException e) {
                log.error("下载文件时发生错误: {}", url, e);
                zos.close();
                fos.close();
                Files.delete(Paths.get(zipFilePath));
                throw new IOException("下载文件时发生错误: " + url, e);
            }
        }

        zos.close();
        fos.close();
    }

    private static String getUniqueFileName(Set<String> uniqueFileNames, String fileName) {
        String uniqueFileName = fileName;
        int counter = 1;
        while (uniqueFileNames.contains(uniqueFileName)) {
            uniqueFileName = fileName + "_" + counter++;
        }
        uniqueFileNames.add(uniqueFileName);
        return uniqueFileName;
    }
}