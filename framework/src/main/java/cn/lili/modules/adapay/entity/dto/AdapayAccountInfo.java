package cn.lili.modules.adapay.entity.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AccountInfo结算对象
 *
 * <AUTHOR>
 * @since 2020/12/7 15:50
 */
@Data
public class AdapayAccountInfo {
    @Size(min = 1, max = 64)
    @NotBlank(message = "银行卡号不能为空")
    @ApiModelProperty(value = "银行卡号")
    private String cardId;

    @Size(min = 1, max = 64)
    @NotBlank(message = "银行卡对应的户名不能为空")
    @ApiModelProperty(value = "银行卡对应的户名")
    private String cardName;

    @Size(min = 1, max = 64)
    @ApiModelProperty(value = "证件号，银行账户类型为对私时必填")
    private String certId;

    @Size(min = 1, max = 2)
    @ApiModelProperty(value = "证件类型，仅支持: 00-身份证，银行账户类型为对私时必填")
    private String certType;

    @Size(min = 1, max = 64)
    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(value = "手机号")
    private String telNo;

    @Size(min = 1, max = 8)
    @ApiModelProperty(value = "银行编码，详见附录银行代码，银行账户类型对公时必填")
    private String bankCode;

    @Size(min = 1, max = 64)
    @ApiModelProperty(value = "开户银行名称")
    private String bankName;

    @Size(min = 1, max = 1)
    @NotBlank(message = "银行账户类型不能为空")
    @ApiModelProperty(value = "银行账户类型: 1-对公; 2-对私")
    private String bankAcctType;

    @Size(min = 1, max = 4)
    @ApiModelProperty(value = "银行账户开户银行所在省份编码 (省市编码)，银行账户类型为对公时必填")
    private String provCode;

    @Size(min = 1, max = 4)
    @ApiModelProperty(value = "银行账户开户银行所在地区编码(省市编码)，银行账户类型为对公时必填")
    private String areaCode;
}
