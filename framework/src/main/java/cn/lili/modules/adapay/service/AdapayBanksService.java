package cn.lili.modules.adapay.service;

import cn.lili.modules.adapay.entity.dos.AdapayBanks;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.cache.annotation.CacheConfig;

import java.util.List;

/**
 * 分账系统银行代码业务层
 *
 * <AUTHOR>
 * @since 2020/12/2 14:14
 */
@CacheConfig(cacheNames = "{adapayBanks}")
public interface AdapayBanksService extends IService<AdapayBanks> {
    /**
     * 获取银行列表
     *
     * @return 银行列表
     */
    List<AdapayBanks> getItem();

}