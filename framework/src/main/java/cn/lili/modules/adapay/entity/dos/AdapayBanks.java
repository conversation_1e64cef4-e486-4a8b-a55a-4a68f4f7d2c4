package cn.lili.modules.adapay.entity.dos;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 分账系统银行代码
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@TableName("li_adapay_banks")
@ApiModel(value = "分账系统银行代码")
public class AdapayBanks extends BaseEntity {

    private static final long serialVersionUID = 418341656517240988L;

    @ApiModelProperty(value = "银行代码")
    private String bankCode;

    @NotEmpty(message = "银行名称不能为空")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @NotNull(message = "排序不能为空")
    @ApiModelProperty(value = "排序")
    private Integer orderNum;

}