package cn.lili.common.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * QRCodeUtil 测试类
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public class QRCodeUtilTest {

    @Test
    public void testCreateQRCode() {
        String content = "https://www.example.com/test";
        String qrCodeBase64 = QRCodeUtil.createQRCode(content);
        
        assertNotNull(qrCodeBase64, "二维码生成结果不应为空");
        assertTrue(qrCodeBase64.startsWith("data:image/png;base64,"), "应该返回Base64格式的PNG图片");
        assertTrue(qrCodeBase64.length() > 100, "Base64字符串长度应该大于100");
    }

    @Test
    public void testCreateQRCodeWithCustomSize() {
        String content = "https://www.example.com/test";
        String qrCodeBase64 = QRCodeUtil.createQRCode(content, 200, 200);
        
        assertNotNull(qrCodeBase64, "自定义尺寸二维码生成结果不应为空");
        assertTrue(qrCodeBase64.startsWith("data:image/png;base64,"), "应该返回Base64格式的PNG图片");
    }

    @Test
    public void testCreateQRCodeBytes() {
        String content = "https://www.example.com/test";
        byte[] qrCodeBytes = QRCodeUtil.createQRCodeBytes(content);
        
        assertNotNull(qrCodeBytes, "二维码字节数组不应为空");
        assertTrue(qrCodeBytes.length > 0, "字节数组长度应该大于0");
    }

    @Test
    public void testCreateQRCodeWithEmptyContent() {
        assertThrows(RuntimeException.class, () -> {
            QRCodeUtil.createQRCode("");
        }, "空内容应该抛出异常");
    }

    @Test
    public void testCreateQRCodeWithNullContent() {
        assertThrows(RuntimeException.class, () -> {
            QRCodeUtil.createQRCode(null);
        }, "null内容应该抛出异常");
    }
}