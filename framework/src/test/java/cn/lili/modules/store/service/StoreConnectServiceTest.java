package cn.lili.modules.store.service;

import cn.lili.modules.connect.entity.enums.SourceEnum;
import cn.lili.modules.store.entity.dos.StoreConnect;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商家微信绑定服务测试
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class StoreConnectServiceTest {

    @Autowired
    private StoreConnectService storeConnectService;

    private static final String TEST_STORE_ID = "test_store_001";
    private static final String TEST_UNION_ID = "test_union_id_001";
    private static final String TEST_UNION_TYPE = SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name();

    @Test
    public void testBindStoreWechat() {
        // 测试绑定商家微信
        boolean result = storeConnectService.bindStoreWechat(TEST_STORE_ID, TEST_UNION_ID, TEST_UNION_TYPE);
        assertTrue(result, "绑定商家微信应该成功");

        // 验证绑定信息
        StoreConnect storeConnect = storeConnectService.getByStoreIdAndType(TEST_STORE_ID, TEST_UNION_TYPE);
        assertNotNull(storeConnect, "应该能够获取到绑定信息");
        assertEquals(TEST_STORE_ID, storeConnect.getStoreId(), "店铺ID应该匹配");
        assertEquals(TEST_UNION_ID, storeConnect.getUnionId(), "UnionId应该匹配");
        assertEquals(TEST_UNION_TYPE, storeConnect.getUnionType(), "绑定类型应该匹配");
        assertTrue(storeConnect.getEnableNotification(), "默认应该启用通知");
    }

    @Test
    public void testUnbindStoreWechat() {
        // 先绑定
        storeConnectService.bindStoreWechat(TEST_STORE_ID, TEST_UNION_ID, TEST_UNION_TYPE);

        // 测试解绑
        boolean result = storeConnectService.unbindStoreWechat(TEST_STORE_ID, TEST_UNION_TYPE);
        assertTrue(result, "解绑商家微信应该成功");

        // 验证解绑结果
        StoreConnect storeConnect = storeConnectService.getByStoreIdAndType(TEST_STORE_ID, TEST_UNION_TYPE);
        assertNull(storeConnect, "解绑后应该获取不到绑定信息");
    }

    @Test
    public void testGetByStoreId() {
        // 绑定多个类型
        storeConnectService.bindStoreWechat(TEST_STORE_ID, TEST_UNION_ID, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        storeConnectService.bindStoreWechat(TEST_STORE_ID, TEST_UNION_ID + "_mp", SourceEnum.WECHAT_MP_OPEN_ID.name());

        // 测试获取店铺的所有绑定信息
        List<StoreConnect> storeConnects = storeConnectService.getByStoreId(TEST_STORE_ID);
        assertEquals(2, storeConnects.size(), "应该有2个绑定信息");
    }

    @Test
    public void testUpdateNotificationStatus() {
        // 先绑定
        storeConnectService.bindStoreWechat(TEST_STORE_ID, TEST_UNION_ID, TEST_UNION_TYPE);

        // 测试禁用通知
        boolean result = storeConnectService.updateNotificationStatus(TEST_STORE_ID, TEST_UNION_TYPE, false);
        assertTrue(result, "更新通知状态应该成功");

        // 验证更新结果
        StoreConnect storeConnect = storeConnectService.getByStoreIdAndType(TEST_STORE_ID, TEST_UNION_TYPE);
        assertNotNull(storeConnect, "应该能够获取到绑定信息");
        assertFalse(storeConnect.getEnableNotification(), "通知应该被禁用");

        // 测试启用通知
        result = storeConnectService.updateNotificationStatus(TEST_STORE_ID, TEST_UNION_TYPE, true);
        assertTrue(result, "更新通知状态应该成功");

        storeConnect = storeConnectService.getByStoreIdAndType(TEST_STORE_ID, TEST_UNION_TYPE);
        assertTrue(storeConnect.getEnableNotification(), "通知应该被启用");
    }

    @Test
    public void testRebindStoreWechat() {
        // 先绑定
        storeConnectService.bindStoreWechat(TEST_STORE_ID, TEST_UNION_ID, TEST_UNION_TYPE);

        // 重新绑定不同的unionId
        String newUnionId = "new_union_id_001";
        boolean result = storeConnectService.bindStoreWechat(TEST_STORE_ID, newUnionId, TEST_UNION_TYPE);
        assertTrue(result, "重新绑定应该成功");

        // 验证绑定信息已更新
        StoreConnect storeConnect = storeConnectService.getByStoreIdAndType(TEST_STORE_ID, TEST_UNION_TYPE);
        assertNotNull(storeConnect, "应该能够获取到绑定信息");
        assertEquals(newUnionId, storeConnect.getUnionId(), "UnionId应该已更新");
    }
}
