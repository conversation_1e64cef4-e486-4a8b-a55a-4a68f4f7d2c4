package cn.lili.modules.wechat.util;

import cn.lili.modules.connect.entity.enums.SourceEnum;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.store.entity.dos.StoreConnect;
import cn.lili.modules.store.service.StoreConnectService;
import cn.lili.modules.system.service.StoreWechatNotificationSettingService;
import cn.lili.modules.wechat.entity.dos.StoreWechatMessage;
import cn.lili.modules.wechat.entity.enums.StoreMessageTypeEnum;
import cn.lili.modules.wechat.service.StoreWechatMessageService;
import cn.lili.modules.wechat.util.WechatAccessTokenUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 商家微信消息工具类测试
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@ExtendWith(MockitoExtension.class)
public class StoreWechatMessageUtilTest {

    @Mock
    private StoreConnectService storeConnectService;

    @Mock
    private OrderService orderService;

    @Mock
    private OrderItemService orderItemService;

    @Mock
    private WechatAccessTokenUtil wechatAccessTokenUtil;

    @Mock
    private StoreWechatMessageService storeWechatMessageService;

    @Mock
    private StoreWechatNotificationSettingService storeWechatNotificationSettingService;

    @InjectMocks
    private StoreWechatMessageUtil storeWechatMessageUtil;

    private Order testOrder;
    private StoreConnect testStoreConnect;
    private StoreWechatMessage testWechatMessage;
    private List<OrderItem> testOrderItems;

    @BeforeEach
    public void setUp() {
        // 准备测试数据
        testOrder = new Order();
        testOrder.setSn("TEST_ORDER_001");
        testOrder.setStoreId("TEST_STORE_001");
        testOrder.setStoreName("测试店铺");
        testOrder.setMemberName("测试用户");
        testOrder.setOrderStatus(OrderStatusEnum.PAID.name());

        testStoreConnect = new StoreConnect();
        testStoreConnect.setStoreId("TEST_STORE_001");
        testStoreConnect.setUnionId("TEST_UNION_ID");
        testStoreConnect.setUnionType(SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        testStoreConnect.setEnableNotification(true);

        testWechatMessage = new StoreWechatMessage();
        testWechatMessage.setCode("TEST_TEMPLATE_ID");
        testWechatMessage.setFirst("您有新的订单付款");
        testWechatMessage.setRemark("请及时处理订单");
        testWechatMessage.setKeywords("ORDER_SN,MEMBER_NAME,PRICE,GOODS_INFO");

        OrderItem orderItem = new OrderItem();
        orderItem.setGoodsName("测试商品");
        orderItem.setNum(2);
        testOrderItems = Arrays.asList(orderItem);
    }

    @Test
    public void testSendStoreWechatMessage_NotificationDisabled() {
        // 模拟通知被禁用
        when(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()))
                .thenReturn(false);

        // 调用方法
        storeWechatMessageUtil.sendStoreWechatMessage("TEST_ORDER_001", StoreMessageTypeEnum.ORDER_PAID);

        // 验证不会调用发送方法
        verify(orderService, never()).getBySn(anyString());
        verify(storeConnectService, never()).getByStoreIdAndType(anyString(), anyString());
    }

    @Test
    public void testSendStoreWechatMessage_OutOfTimeRange() {
        // 模拟通知启用但不在时间范围内
        when(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()))
                .thenReturn(true);
        when(storeWechatNotificationSettingService.isInNotificationTimeRange())
                .thenReturn(false);

        // 调用方法
        storeWechatMessageUtil.sendStoreWechatMessage("TEST_ORDER_001", StoreMessageTypeEnum.ORDER_PAID);

        // 验证不会调用发送方法
        verify(orderService, never()).getBySn(anyString());
        verify(storeConnectService, never()).getByStoreIdAndType(anyString(), anyString());
    }

    @Test
    public void testSendStoreWechatMessage_NoStoreConnect() {
        // 模拟通知启用且在时间范围内
        when(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()))
                .thenReturn(true);
        when(storeWechatNotificationSettingService.isInNotificationTimeRange())
                .thenReturn(true);

        // 模拟订单存在
        when(orderService.getBySn("TEST_ORDER_001")).thenReturn(testOrder);

        // 模拟商家未绑定微信
        when(storeConnectService.getByStoreIdAndType("TEST_STORE_001", SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name()))
                .thenReturn(null);

        // 调用方法
        storeWechatMessageUtil.sendStoreWechatMessage("TEST_ORDER_001", StoreMessageTypeEnum.ORDER_PAID);

        // 验证调用了相关方法但不会发送消息
        verify(orderService, times(2)).getBySn("TEST_ORDER_001"); // 公众号和小程序各调用一次
        verify(storeConnectService).getByStoreIdAndType("TEST_STORE_001", SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        verify(storeWechatMessageService, never()).getByOrderStatusAndType(anyString(), anyString());
    }

    @Test
    public void testSendStoreWechatMessage_NotificationDisabledForStore() {
        // 模拟通知启用且在时间范围内
        when(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()))
                .thenReturn(true);
        when(storeWechatNotificationSettingService.isInNotificationTimeRange())
                .thenReturn(true);

        // 模拟订单存在
        when(orderService.getBySn("TEST_ORDER_001")).thenReturn(testOrder);

        // 模拟商家绑定了微信但禁用了通知
        StoreConnect disabledStoreConnect = new StoreConnect();
        disabledStoreConnect.setStoreId("TEST_STORE_001");
        disabledStoreConnect.setUnionId("TEST_UNION_ID");
        disabledStoreConnect.setUnionType(SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        disabledStoreConnect.setEnableNotification(false);

        when(storeConnectService.getByStoreIdAndType("TEST_STORE_001", SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name()))
                .thenReturn(disabledStoreConnect);

        // 调用方法
        storeWechatMessageUtil.sendStoreWechatMessage("TEST_ORDER_001", StoreMessageTypeEnum.ORDER_PAID);

        // 验证不会获取消息模板
        verify(storeWechatMessageService, never()).getByOrderStatusAndType(anyString(), anyString());
    }

    @Test
    public void testSendStoreWechatMessage_NoMessageTemplate() {
        // 模拟通知启用且在时间范围内
        when(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()))
                .thenReturn(true);
        when(storeWechatNotificationSettingService.isInNotificationTimeRange())
                .thenReturn(true);

        // 模拟订单存在
        when(orderService.getBySn("TEST_ORDER_001")).thenReturn(testOrder);

        // 模拟商家绑定了微信且启用了通知
        when(storeConnectService.getByStoreIdAndType("TEST_STORE_001", SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name()))
                .thenReturn(testStoreConnect);

        // 模拟没有消息模板
        when(storeWechatMessageService.getByOrderStatusAndType(OrderStatusEnum.PAID.name(), StoreMessageTypeEnum.ORDER_PAID.name()))
                .thenReturn(null);

        // 调用方法
        storeWechatMessageUtil.sendStoreWechatMessage("TEST_ORDER_001", StoreMessageTypeEnum.ORDER_PAID);

        // 验证调用了获取模板的方法
        verify(storeWechatMessageService).getByOrderStatusAndType(OrderStatusEnum.PAID.name(), StoreMessageTypeEnum.ORDER_PAID.name());
        // 但不会获取token（因为没有模板）
        verify(wechatAccessTokenUtil, never()).cgiAccessToken(any());
    }
}
