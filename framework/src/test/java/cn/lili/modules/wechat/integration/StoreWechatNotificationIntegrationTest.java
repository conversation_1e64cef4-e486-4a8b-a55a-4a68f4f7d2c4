package cn.lili.modules.wechat.integration;

import cn.lili.modules.connect.entity.enums.SourceEnum;
import cn.lili.modules.order.order.entity.dto.OrderMessage;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.store.entity.dos.StoreConnect;
import cn.lili.modules.store.service.StoreConnectService;
import cn.lili.modules.system.entity.dto.StoreWechatNotificationSetting;
import cn.lili.modules.system.service.StoreWechatNotificationSettingService;
import cn.lili.modules.wechat.entity.enums.StoreMessageTypeEnum;
import cn.lili.modules.wechat.util.StoreWechatMessageUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商家微信通知集成测试
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class StoreWechatNotificationIntegrationTest {

    @Autowired
    private StoreConnectService storeConnectService;

    @Autowired
    private StoreWechatNotificationSettingService storeWechatNotificationSettingService;

    @Autowired
    private StoreWechatMessageUtil storeWechatMessageUtil;

    private static final String TEST_STORE_ID = "integration_test_store_001";
    private static final String TEST_UNION_ID = "integration_test_union_id_001";
    private static final String TEST_ORDER_SN = "integration_test_order_001";

    @BeforeEach
    public void setUp() {
        // 清理测试数据
        storeConnectService.unbindStoreWechat(TEST_STORE_ID, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        storeConnectService.unbindStoreWechat(TEST_STORE_ID, SourceEnum.WECHAT_MP_OPEN_ID.name());
    }

    @Test
    public void testCompleteStoreWechatNotificationFlow() {
        // 1. 配置商家微信通知设置
        StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
        setting.setEnableStoreNotification(true);
        setting.setEnableOrderPaidNotification(true);
        setting.setEnableOrderDeliveredNotification(false);
        setting.setNotificationTimeLimit(0); // 无时间限制

        boolean saveResult = storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);
        assertTrue(saveResult, "保存商家微信通知设置应该成功");

        // 2. 绑定商家微信公众号
        boolean bindResult = storeConnectService.bindStoreWechat(
                TEST_STORE_ID, TEST_UNION_ID, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        assertTrue(bindResult, "绑定商家微信公众号应该成功");

        // 3. 验证绑定信息
        StoreConnect storeConnect = storeConnectService.getByStoreIdAndType(
                TEST_STORE_ID, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        assertNotNull(storeConnect, "应该能够获取到绑定信息");
        assertTrue(storeConnect.getEnableNotification(), "通知应该默认启用");

        // 4. 测试通知配置检查
        assertTrue(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()),
                "订单付款通知应该启用");
        assertFalse(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_DELIVERED.name()),
                "订单发货通知应该禁用");
        assertTrue(storeWechatNotificationSettingService.isInNotificationTimeRange(),
                "应该在通知时间范围内");

        // 5. 测试发送通知（由于没有真实的微信环境，这里主要测试流程不报错）
        try {
            storeWechatMessageUtil.sendStoreWechatMessage(TEST_ORDER_SN, StoreMessageTypeEnum.ORDER_PAID);
            // 如果没有抛出异常，说明流程正常
        } catch (Exception e) {
            // 在测试环境中，由于没有真实的订单和微信配置，可能会有异常，这是正常的
            // 主要是验证配置检查部分的逻辑
        }
    }

    @Test
    public void testStoreWechatNotificationWithDisabledSetting() {
        // 1. 配置禁用商家微信通知
        StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
        setting.setEnableStoreNotification(false);
        setting.setEnableOrderPaidNotification(true);

        storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);

        // 2. 绑定商家微信
        storeConnectService.bindStoreWechat(
                TEST_STORE_ID, TEST_UNION_ID, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());

        // 3. 验证通知被禁用
        assertFalse(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()),
                "总开关禁用时，订单付款通知应该被禁用");

        // 4. 测试发送通知（应该被配置阻止）
        try {
            storeWechatMessageUtil.sendStoreWechatMessage(TEST_ORDER_SN, StoreMessageTypeEnum.ORDER_PAID);
            // 由于通知被禁用，应该直接返回，不会有异常
        } catch (Exception e) {
            fail("禁用通知时不应该抛出异常");
        }
    }

    @Test
    public void testStoreWechatNotificationWithTimeLimit() {
        // 1. 配置有时间限制的商家微信通知
        StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
        setting.setEnableStoreNotification(true);
        setting.setEnableOrderPaidNotification(true);
        setting.setNotificationTimeLimit(1);
        setting.setNotificationStartHour(1);  // 凌晨1点开始
        setting.setNotificationEndHour(2);    // 凌晨2点结束

        storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);

        // 2. 绑定商家微信
        storeConnectService.bindStoreWechat(
                TEST_STORE_ID, TEST_UNION_ID, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());

        // 3. 验证通知启用但可能不在时间范围内（取决于测试运行时间）
        assertTrue(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()),
                "订单付款通知应该启用");

        // 时间范围检查结果取决于测试运行的具体时间
        boolean inTimeRange = storeWechatNotificationSettingService.isInNotificationTimeRange();
        assertNotNull(inTimeRange, "时间范围检查应该返回结果");
    }

    @Test
    public void testMultipleStoreConnectTypes() {
        // 1. 绑定多种类型的微信
        boolean bindPublicResult = storeConnectService.bindStoreWechat(
                TEST_STORE_ID, TEST_UNION_ID, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        assertTrue(bindPublicResult, "绑定微信公众号应该成功");

        boolean bindMpResult = storeConnectService.bindStoreWechat(
                TEST_STORE_ID, TEST_UNION_ID + "_mp", SourceEnum.WECHAT_MP_OPEN_ID.name());
        assertTrue(bindMpResult, "绑定微信小程序应该成功");

        // 2. 验证绑定信息
        assertEquals(2, storeConnectService.getByStoreId(TEST_STORE_ID).size(),
                "应该有2个绑定信息");

        // 3. 禁用其中一种类型的通知
        boolean updateResult = storeConnectService.updateNotificationStatus(
                TEST_STORE_ID, SourceEnum.WECHAT_MP_OPEN_ID.name(), false);
        assertTrue(updateResult, "更新通知状态应该成功");

        // 4. 验证更新结果
        StoreConnect publicConnect = storeConnectService.getByStoreIdAndType(
                TEST_STORE_ID, SourceEnum.WECHAT_OFFIACCOUNT_OPEN_ID.name());
        StoreConnect mpConnect = storeConnectService.getByStoreIdAndType(
                TEST_STORE_ID, SourceEnum.WECHAT_MP_OPEN_ID.name());

        assertTrue(publicConnect.getEnableNotification(), "公众号通知应该启用");
        assertFalse(mpConnect.getEnableNotification(), "小程序通知应该禁用");
    }
}
