package cn.lili.modules.system.service;

import cn.lili.modules.system.entity.dto.StoreWechatNotificationSetting;
import cn.lili.modules.wechat.entity.enums.StoreMessageTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商家微信通知设置服务测试
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class StoreWechatNotificationSettingServiceTest {

    @Autowired
    private StoreWechatNotificationSettingService storeWechatNotificationSettingService;

    @Test
    public void testGetDefaultSetting() {
        // 测试获取默认设置
        StoreWechatNotificationSetting setting = storeWechatNotificationSettingService.getStoreWechatNotificationSetting();
        assertNotNull(setting, "应该能够获取到设置");
        assertTrue(setting.getEnableStoreNotification(), "默认应该启用商家通知");
        assertTrue(setting.getEnableOrderPaidNotification(), "默认应该启用订单付款通知");
        assertFalse(setting.getEnableOrderDeliveredNotification(), "默认应该禁用订单发货通知");
        assertEquals(0, setting.getNotificationTimeLimit(), "默认应该没有时间限制");
    }

    @Test
    public void testSaveAndGetSetting() {
        // 创建测试设置
        StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
        setting.setEnableStoreNotification(true);
        setting.setEnableOrderPaidNotification(true);
        setting.setEnableOrderDeliveredNotification(true);
        setting.setEnableOrderCompletedNotification(false);
        setting.setEnableOrderCancelledNotification(false);
        setting.setNotificationTimeLimit(1);
        setting.setNotificationStartHour(9);
        setting.setNotificationEndHour(22);

        // 保存设置
        boolean result = storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);
        assertTrue(result, "保存设置应该成功");

        // 获取设置并验证
        StoreWechatNotificationSetting savedSetting = storeWechatNotificationSettingService.getStoreWechatNotificationSetting();
        assertNotNull(savedSetting, "应该能够获取到保存的设置");
        assertTrue(savedSetting.getEnableStoreNotification(), "商家通知应该启用");
        assertTrue(savedSetting.getEnableOrderPaidNotification(), "订单付款通知应该启用");
        assertTrue(savedSetting.getEnableOrderDeliveredNotification(), "订单发货通知应该启用");
        assertFalse(savedSetting.getEnableOrderCompletedNotification(), "订单完成通知应该禁用");
        assertFalse(savedSetting.getEnableOrderCancelledNotification(), "订单取消通知应该禁用");
        assertEquals(1, savedSetting.getNotificationTimeLimit(), "时间限制应该匹配");
        assertEquals(9, savedSetting.getNotificationStartHour(), "开始时间应该匹配");
        assertEquals(22, savedSetting.getNotificationEndHour(), "结束时间应该匹配");
    }

    @Test
    public void testIsNotificationEnabled() {
        // 创建测试设置
        StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
        setting.setEnableStoreNotification(true);
        setting.setEnableOrderPaidNotification(true);
        setting.setEnableOrderDeliveredNotification(false);
        setting.setEnableOrderCompletedNotification(false);
        setting.setEnableOrderCancelledNotification(false);

        // 保存设置
        storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);

        // 测试各种消息类型的启用状态
        assertTrue(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()),
                "订单付款通知应该启用");
        assertFalse(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_DELIVERED.name()),
                "订单发货通知应该禁用");
        assertFalse(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_COMPLETED.name()),
                "订单完成通知应该禁用");
        assertFalse(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_CANCELLED.name()),
                "订单取消通知应该禁用");
    }

    @Test
    public void testIsNotificationEnabledWithDisabledStoreNotification() {
        // 创建测试设置，禁用商家通知总开关
        StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
        setting.setEnableStoreNotification(false);
        setting.setEnableOrderPaidNotification(true);

        // 保存设置
        storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);

        // 测试即使单个类型启用，但总开关禁用时应该返回false
        assertFalse(storeWechatNotificationSettingService.isNotificationEnabled(StoreMessageTypeEnum.ORDER_PAID.name()),
                "总开关禁用时，所有通知都应该禁用");
    }

    @Test
    public void testIsInNotificationTimeRange() {
        // 测试无时间限制的情况
        StoreWechatNotificationSetting setting = new StoreWechatNotificationSetting();
        setting.setNotificationTimeLimit(0);
        storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);

        assertTrue(storeWechatNotificationSettingService.isInNotificationTimeRange(),
                "无时间限制时应该总是返回true");

        // 测试有时间限制的情况（这个测试可能会因为运行时间而有所不同）
        setting.setNotificationTimeLimit(1);
        setting.setNotificationStartHour(0);  // 0点开始
        setting.setNotificationEndHour(23);   // 23点结束
        storeWechatNotificationSettingService.saveStoreWechatNotificationSetting(setting);

        // 由于测试运行时间不确定，这里只验证方法能正常执行
        boolean result = storeWechatNotificationSettingService.isInNotificationTimeRange();
        assertNotNull(result, "时间范围检查应该返回结果");
    }

    @Test
    public void testIsNotificationEnabledWithInvalidMessageType() {
        // 测试无效的消息类型
        boolean result = storeWechatNotificationSettingService.isNotificationEnabled("INVALID_MESSAGE_TYPE");
        assertFalse(result, "无效的消息类型应该返回false");
    }
}
