# 测试环境配置
spring:
  datasource:
    # 使用H2内存数据库进行测试
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    
  h2:
    console:
      enabled: true

# 日志配置
logging:
  level:
    cn.lili: DEBUG
    org.springframework: WARN
    org.hibernate: WARN

# 禁用一些在测试中不需要的功能
lili:
  data:
    rocketmq:
      order-topic: test_order_topic
      order-group: test_order_group
