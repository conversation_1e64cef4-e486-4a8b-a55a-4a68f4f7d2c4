package cn.lili.event.impl;

import cn.lili.event.OrderStatusChangeEvent;
import cn.lili.modules.order.order.entity.dto.OrderMessage;
import cn.lili.modules.wechat.entity.enums.StoreMessageTypeEnum;
import cn.lili.modules.wechat.util.StoreWechatMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 商家微信消息执行器
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class StoreWechatMessageExecute implements OrderStatusChangeEvent {

    @Autowired
    private StoreWechatMessageUtil storeWechatMessageUtil;

    @Override
    public void orderChange(OrderMessage orderMessage) {
        try {
            // 根据订单状态发送对应的商家微信通知
            switch (orderMessage.getNewStatus()) {
                case PAID:
                case UNDELIVERED:
                    // 订单付款通知
                    storeWechatMessageUtil.sendStoreWechatMessage(
                            orderMessage.getOrderSn(), StoreMessageTypeEnum.ORDER_PAID);
                    break;
                case DELIVERED:
                    // 订单发货通知（如果需要的话）
                    // storeWechatMessageUtil.sendStoreWechatMessage(
                    //         orderMessage.getOrderSn(), StoreMessageTypeEnum.ORDER_DELIVERED);
                    break;
                case COMPLETED:
                    // 订单完成通知（如果需要的话）
                    // storeWechatMessageUtil.sendStoreWechatMessage(
                    //         orderMessage.getOrderSn(), StoreMessageTypeEnum.ORDER_COMPLETED);
                    break;
                case CANCELLED:
                    // 订单取消通知（如果需要的话）
                    // storeWechatMessageUtil.sendStoreWechatMessage(
                    //         orderMessage.getOrderSn(), StoreMessageTypeEnum.ORDER_CANCELLED);
                    break;
                default:
                    // 其他状态暂不处理
                    break;
            }
        } catch (Exception e) {
            log.error("发送商家微信通知失败，订单号：{}", orderMessage.getOrderSn(), e);
        }
    }
}
